import React, { forwardRef, useImperative<PERSON><PERSON>le, useCallback } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import BottomSheet, { BottomSheetView } from "@gorhom/bottom-sheet";
import { Edit3, Package, Trash2, X, Share } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";

interface PostOptionsBottomSheetProps {
  postSource: "business" | "customer";
  isOwner: boolean;
  onEditPost: () => void;
  onEditProducts?: () => void;
  onDeletePost: () => void;
  onSharePost: () => void;
}

export interface PostOptionsBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

const PostOptionsBottomSheet = forwardRef<
  PostOptionsBottomSheetRef,
  PostOptionsBottomSheetProps
>(
  (
    {
      postSource,
      isOwner,
      onEditPost,
      onEditProducts,
      onDeletePost,
      onSharePost,
    },
    ref
  ) => {
    const { colors, isDark } = useTheme();
    const bottomSheetRef = React.useRef<BottomSheet>(null);

    // Snap points for the bottom sheet with multiple breakpoints for expand/collapse
    const snapPoints = React.useMemo(() => {
      if (!isOwner) {
        // Only share option for non-owners - provide expand/collapse capability
        return ["25%", "50%"];
      }
      // Multiple breakpoints for owners - allow expand/collapse between 50% and 75%
      return postSource === "business" ? ["50%", "75%"] : ["50%", "65%"];
    }, [postSource, isOwner]);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        // Use expand() to open to the highest snap point for better visibility
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleEditPost = useCallback(() => {
      bottomSheetRef.current?.close();
      setTimeout(() => {
        onEditPost();
      }, 300);
    }, [onEditPost]);

    const handleEditProducts = useCallback(() => {
      if (onEditProducts) {
        bottomSheetRef.current?.close();
        setTimeout(() => {
          onEditProducts();
        }, 300);
      }
    }, [onEditProducts]);

    const handleDeletePost = useCallback(() => {
      bottomSheetRef.current?.close();
      setTimeout(() => {
        onDeletePost();
      }, 300);
    }, [onDeletePost]);

    const handleSharePost = useCallback(() => {
      bottomSheetRef.current?.close();
      setTimeout(() => {
        onSharePost();
      }, 300);
    }, [onSharePost]);

    const handleClose = useCallback(() => {
      bottomSheetRef.current?.close();
    }, []);

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        enablePanDownToClose
        backgroundStyle={{
          backgroundColor: isDark ? "#000000" : "#ffffff",
        }}
        handleIndicatorStyle={{
          backgroundColor: colors.textSecondary,
        }}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          <View
            style={[
              styles.header,
              {
                borderBottomColor: colors.border,
              },
            ]}
          >
            <Text style={[styles.headerTitle, { color: colors.textPrimary }]}>
              Post Options
            </Text>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <X size={24} color={colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Options */}
          <View style={styles.optionsContainer}>
            {/* Edit/Delete options - Only for post owners */}
            {isOwner && (
              <>
                {/* Edit Post Option */}
                <TouchableOpacity
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={handleEditPost}
                  activeOpacity={0.7}
                >
                  <View
                    style={[
                      styles.optionIconContainer,
                      { backgroundColor: colors.primary + "20" },
                    ]}
                  >
                    <Edit3 size={20} color={colors.primary} />
                  </View>
                  <View style={styles.optionTextContainer}>
                    <Text
                      style={[
                        styles.optionTitle,
                        { color: colors.textPrimary },
                      ]}
                    >
                      Edit Post
                    </Text>
                    <Text
                      style={[
                        styles.optionDescription,
                        { color: colors.textSecondary },
                      ]}
                    >
                      Modify the content of this post
                    </Text>
                  </View>
                </TouchableOpacity>

                {/* Edit Products Option - Only for business posts and when onEditProducts is provided */}
                {postSource === "business" && onEditProducts && (
                  <TouchableOpacity
                    style={[
                      styles.optionButton,
                      {
                        backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                        borderColor: colors.border,
                      },
                    ]}
                    onPress={handleEditProducts}
                    activeOpacity={0.7}
                  >
                    <View
                      style={[
                        styles.optionIconContainer,
                        { backgroundColor: colors.primary + "20" },
                      ]}
                    >
                      <Package size={20} color={colors.primary} />
                    </View>
                    <View style={styles.optionTextContainer}>
                      <Text
                        style={[
                          styles.optionTitle,
                          { color: colors.textPrimary },
                        ]}
                      >
                        Edit Products
                      </Text>
                      <Text
                        style={[
                          styles.optionDescription,
                          { color: colors.textSecondary },
                        ]}
                      >
                        Modify linked products for this post
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
              </>
            )}

            {/* Share Post Option */}
            <TouchableOpacity
              style={[
                styles.optionButton,
                {
                  backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                  borderColor: colors.border,
                },
              ]}
              onPress={handleSharePost}
              activeOpacity={0.7}
            >
              <View
                style={[
                  styles.optionIconContainer,
                  { backgroundColor: colors.primary + "20" },
                ]}
              >
                <Share size={20} color={colors.primary} />
              </View>
              <View style={styles.optionTextContainer}>
                <Text
                  style={[styles.optionTitle, { color: colors.textPrimary }]}
                >
                  Share Post
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    { color: colors.textSecondary },
                  ]}
                >
                  Share this post with others
                </Text>
              </View>
            </TouchableOpacity>

            {/* Delete Post Option - Only for post owners */}
            {isOwner && (
              <TouchableOpacity
                style={[
                  styles.optionButton,
                  {
                    backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                    borderColor: colors.border,
                  },
                ]}
                onPress={handleDeletePost}
                activeOpacity={0.7}
              >
                <View
                  style={[
                    styles.optionIconContainer,
                    { backgroundColor: "#EF444420" },
                  ]}
                >
                  <Trash2 size={20} color="#EF4444" />
                </View>
                <View style={styles.optionTextContainer}>
                  <Text style={[styles.optionTitle, { color: "#EF4444" }]}>
                    Delete Post
                  </Text>
                  <Text
                    style={[
                      styles.optionDescription,
                      { color: colors.textSecondary },
                    ]}
                  >
                    Permanently remove this post
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

PostOptionsBottomSheet.displayName = "PostOptionsBottomSheet";

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  closeButton: {
    padding: 4,
  },
  optionsContainer: {
    padding: 20,
    gap: 12,
  },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  optionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

export default PostOptionsBottomSheet;
