"use server";

import { createClient } from "@/utils/supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { Database } from "@/types/supabase";
import {
  pauseSubscription as razorpayPauseSubscription,
  resumeSubscription as razorpayResumeSubscription,
  getSubscription as razorpayGetSubscription,
} from "@/lib/razorpay/services/subscription";
import { PricingPlan, pricingPlans } from "@/lib/PricingPlans";
import { ActionResponse } from "./types";
import {
  getUserAndProfile,
  revalidateSubscriptionPaths,
  createErrorResponse,
  createSuccessResponse,
} from "./utils";
import { SUBSCRIPTION_STATUS } from "@/lib/razorpay/webhooks/handlers/subscription-constants";

/**
 * Pause a subscription
 * @returns The pause result
 */
export async function pauseUserSubscription(): Promise<ActionResponse> {
  const supabase = (await createClient()) as SupabaseClient<Database>;
  try {
    // Get user and profile with subscription information
    const { user, profile, error } = await getUserAndProfile(
      "has_active_subscription"
    );

    if (error) {
      return createErrorResponse(error);
    }

    // If profile is null or user doesn't have an active subscription, return error
    if (!profile || !profile.has_active_subscription) {
      return createErrorResponse("User does not have an active subscription");
    }

    // Get the subscription ID from the payment_subscriptions table using centralized logic
    const _supabase = await createClient();
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("razorpay_subscription_id")
      .eq("business_profile_id", profile.id)
      .eq("subscription_status", "active");

    if (subscriptionError || !subscriptions || subscriptions.length === 0) {
      console.error(
        "[RAZORPAY_ERROR] Error fetching subscription:",
        subscriptionError
      );
      return createErrorResponse("Could not find an active subscription");
    }

    const subscriptionId = subscriptions[0].razorpay_subscription_id;

    if (!subscriptionId) {
      return createErrorResponse("No active subscription found");
    }

    // Pause the subscription in Razorpay
    const result = await razorpayPauseSubscription(subscriptionId, "now");

    if (!result.success) {
      console.error(
        "[RAZORPAY_ERROR] Error pausing subscription:",
        result.error
      );
      return createErrorResponse("Failed to pause subscription");
    }

    // Get current subscription details to store original plan
    const { data: currentSubscription, error: fetchError } = await supabase
      .from("payment_subscriptions")
      .select("plan_id, plan_cycle")
      .eq("razorpay_subscription_id", subscriptionId)
      .single();

    if (fetchError) {
      console.error(
        "[RAZORPAY_ERROR] Error fetching current subscription:",
        fetchError
      );
      return createErrorResponse(
        "Failed to fetch current subscription details"
      );
    }

    // Update the subscription record in the database
    // Downgrade to free plan and store original plan for restoration
    const now = new Date().toISOString();

    console.log(
      `[RAZORPAY_DEBUG] Pausing subscription: storing original plan ${currentSubscription.plan_id}/${currentSubscription.plan_cycle} and downgrading to free`
    );

    const { error: updateError } = await supabase
      .from("payment_subscriptions")
      .update({
        subscription_status: SUBSCRIPTION_STATUS.ACTIVE, // Keep as active but with free plan
        subscription_paused_at: now,
        updated_at: now,
        // Store original plan for restoration
        original_plan_id: currentSubscription.plan_id,
        original_plan_cycle: currentSubscription.plan_cycle,
        // Downgrade to free plan
        plan_id: "free",
        plan_cycle: "monthly",
      })
      .eq("razorpay_subscription_id", subscriptionId);

    if (updateError) {
      console.error(
        "[RAZORPAY_ERROR] Error updating subscription record:",
        updateError
      );
      return createErrorResponse("Failed to update subscription record");
    }

    console.log(
      `[RAZORPAY_DEBUG] Successfully downgraded subscription to free plan and stored original plan`
    );

    // Update business profile to reflect paused subscription
    // Set has_active_subscription to false since user is now on free plan
    console.log(
      `[RAZORPAY_DEBUG] Updating business profile: setting has_active_subscription to false`
    );

    const { data: atomicResult, error: atomicError } = await supabase.rpc(
      "update_subscription_atomic",
      {
        p_subscription_id: subscriptionId,
        p_new_status: SUBSCRIPTION_STATUS.ACTIVE, // Keep as active but with free plan
        p_business_profile_id: user.id,
        p_has_active_subscription: false, // Set to false since user is now on free plan
        p_additional_data: {
          subscription_paused_at: now,
          status: "offline", // Set business profile to offline when subscription is paused
        } as any,
        p_webhook_timestamp: undefined,
      }
    );

    if (atomicError || !(atomicResult as any)?.success) {
      console.error(
        "[RAZORPAY_ERROR] Error updating subscription atomically:",
        atomicError || (atomicResult as any)?.error
      );
      // Continue anyway, as the Razorpay subscription was paused successfully
    } else {
      console.log(
        `[RAZORPAY_DEBUG] Successfully updated subscription ${subscriptionId} and business profile ${user.id} atomically`
      );
    }

    // Revalidate paths
    revalidateSubscriptionPaths();

    return createSuccessResponse({
      message: "Subscription paused successfully",
      subscription: result.data,
    });
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in pauseUserSubscription:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "An unknown error occurred"
    );
  }
}

/**
 * Activate a paused subscription
 * @returns The activation result
 */
export async function activateUserSubscription(): Promise<ActionResponse> {
  const supabase = (await createClient()) as SupabaseClient<Database>;
  try {
    // Get user and profile
    const { user, profile, error } = await getUserAndProfile(
      "has_active_subscription"
    );

    if (error) {
      return createErrorResponse(error);
    }

    // If profile is null, return error
    if (!profile) {
      return createErrorResponse("User profile not found");
    }

    // Get the subscription ID from the payment_subscriptions table
    // Look for subscriptions that are paused (have subscription_paused_at set)

    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select(
        "razorpay_subscription_id, subscription_paused_at, original_plan_id, original_plan_cycle"
      )
      .eq("business_profile_id", user.id)
      .not("subscription_paused_at", "is", null) // Find paused subscriptions
      .limit(1);

    if (subscriptionError || !subscriptions || subscriptions.length === 0) {
      console.error(
        "[RAZORPAY_ERROR] Error fetching paused subscription:",
        subscriptionError
      );
      return createErrorResponse("Could not find a paused subscription");
    }

    const subscriptionId = subscriptions[0].razorpay_subscription_id;

    if (!subscriptionId) {
      return createErrorResponse("No paused subscription found");
    }

    // Resume the subscription in Razorpay
    const result = await razorpayResumeSubscription(subscriptionId);

    if (!result.success) {
      console.error(
        "[RAZORPAY_ERROR] Error resuming subscription:",
        result.error
      );
      return createErrorResponse("Failed to resume subscription");
    }

    // Get the original plan details for restoration
    const originalPlanId = subscriptions[0].original_plan_id;
    const originalPlanCycle = subscriptions[0].original_plan_cycle;

    if (!originalPlanId || !originalPlanCycle) {
      console.error("[RAZORPAY_ERROR] No original plan found for restoration");
      return createErrorResponse(
        "Cannot resume subscription: original plan not found"
      );
    }

    console.log(
      `[RAZORPAY_DEBUG] Resuming subscription: restoring original plan ${originalPlanId}/${originalPlanCycle}`
    );

    // Restore the original plan
    const { error: restoreError } = await supabase
      .from("payment_subscriptions")
      .update({
        plan_id: originalPlanId,
        plan_cycle: originalPlanCycle,
        subscription_paused_at: null,
        original_plan_id: null, // Clear the stored original plan
        original_plan_cycle: null,
        updated_at: new Date().toISOString(),
      })
      .eq("razorpay_subscription_id", subscriptionId);

    if (restoreError) {
      console.error(
        "[RAZORPAY_ERROR] Error restoring original plan:",
        restoreError
      );
      return createErrorResponse("Failed to restore original plan");
    }

    console.log(
      `[RAZORPAY_DEBUG] Successfully restored original plan ${originalPlanId}/${originalPlanCycle}`
    );

    // Update business profile to reflect resumed subscription
    // Set has_active_subscription to true since user is back on their original plan
    console.log(
      `[RAZORPAY_DEBUG] Updating business profile: setting has_active_subscription to true`
    );

    const { data: atomicResult, error: atomicError } = await supabase.rpc(
      "update_subscription_atomic",
      {
        p_subscription_id: subscriptionId,
        p_new_status: SUBSCRIPTION_STATUS.ACTIVE,
        p_business_profile_id: user.id,
        p_has_active_subscription: true, // Set to true since user is back on their original plan
        p_additional_data: {
          // Note: We don't automatically set status back to "online" as the user needs to
          // explicitly set their card back to online through the dashboard
        },
        p_webhook_timestamp: undefined,
      }
    );

    if (atomicError || !(atomicResult as any)?.success) {
      console.error(
        "[RAZORPAY_ERROR] Error updating subscription atomically:",
        atomicError || (atomicResult as any)?.error
      );
      // Continue anyway, as the Razorpay subscription was resumed successfully
    } else {
      console.log(
        `[RAZORPAY_DEBUG] Successfully updated subscription ${subscriptionId} and business profile ${user.id} atomically`
      );
      console.log(
        `[RAZORPAY_DEBUG] Note: Business profile status remains offline until user explicitly sets it back to online`
      );
    }

    // Revalidate paths
    revalidateSubscriptionPaths();

    // Ensure we're returning a properly structured response
    const response = await createSuccessResponse({
      message: "Subscription resumed successfully",
      subscription: result.data,
    });

    console.log(
      "[RAZORPAY_DEBUG] Returning response from activateUserSubscription:",
      response
    );
    return response;
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in activateUserSubscription:", error);
    const errorResponse = await createErrorResponse(
      error instanceof Error ? error.message : "An unknown error occurred"
    );
    console.log(
      "[RAZORPAY_DEBUG] Returning error response from activateUserSubscription:",
      errorResponse
    );
    return errorResponse;
  }
}

/**
 * Get subscription details
 * @returns The subscription details
 */
export async function getSubscriptionDetails(): Promise<ActionResponse> {
  try {
    // Get user and profile
    // Note: trial_end_date is in business_profiles table, not payment_subscriptions
    // Our updated getUserAndProfile function will handle this correctly
    const { user, profile, error } = await getUserAndProfile(`
      has_active_subscription,
      subscription_start_date,
      trial_end_date,
      cancellation_requested_at,
      subscription_paused_at
    `);

    if (error) {
      return createErrorResponse(error);
    }

    // Check if profile exists
    if (!profile) {
      return createErrorResponse("User profile not found");
    }

    // Get plan details
    let currentPlan: PricingPlan | undefined;

    // Get the subscription from the payment_subscriptions table
    const supabase = (await createClient()) as SupabaseClient<Database>;
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from("payment_subscriptions")
      .select("*")
      .eq("business_profile_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1);

    if (subscriptionError) {
      console.error(
        "[RAZORPAY_ERROR] Error fetching subscription:",
        subscriptionError
      );
    }

    // Check Razorpay subscription status if available
    let subscriptionStatus = null;
    let currentPlanId: string | null = null;
    let planCycle: string | null = null;

    if (subscriptions && subscriptions.length > 0) {
      // Get plan details from subscription
      currentPlanId = subscriptions[0].plan_id;
      planCycle = subscriptions[0].plan_cycle;

      // Get plan details
      if (currentPlanId && planCycle) {
        const allPlans = pricingPlans(planCycle as "monthly" | "yearly");
        currentPlan = allPlans.find((plan) => plan.id === currentPlanId);
      }

      // Get Razorpay subscription status if available
      if (subscriptions[0].razorpay_subscription_id) {
        const subscriptionId = subscriptions[0].razorpay_subscription_id;
        const statusResult = await razorpayGetSubscription(subscriptionId);

        if (statusResult.success) {
          subscriptionStatus = statusResult.data;
        }
      }
    }

    // Refunds are only available for payment issues or subscription problems
    // Always set isWithinRefundWindow to false as the 7-day refund policy has been removed
    const isWithinRefundWindow = false;

    return createSuccessResponse({
      ...profile,
      subscription:
        subscriptions && subscriptions.length > 0 ? subscriptions[0] : null,
      currentPlan,
      subscriptionStatus,
      isWithinRefundWindow,
    });
  } catch (error) {
    console.error("[RAZORPAY_ERROR] Error in getSubscriptionDetails:", error);
    return createErrorResponse(
      error instanceof Error ? error.message : "An unknown error occurred"
    );
  }
}
