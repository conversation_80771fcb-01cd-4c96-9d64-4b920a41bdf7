import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import {
  likesService,
  LikeWithProfile,
} from "@/backend/supabase/services/posts/socialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { LikeCard } from "@/src/components/social/LikeCard";
import { LikesModalSkeleton } from "@/src/components/skeletons/modals/LikesModalSkeleton";
import { createLikesModalStyles } from "@/styles/modals/customer/likes-modal";
import { SocialEmptyState } from "@/src/components/shared/ui/SocialEmptyState";
import { useTheme } from "@/src/hooks/useTheme";
import { SearchComponent } from "@/src/components/social/SearchComponent";

interface LikesListProps {
  searchTerm: string;
  onDiscoverPress: () => void;
}

const LikesList: React.FC<LikesListProps> = ({ searchTerm, onDiscoverPress }) => {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createLikesModalStyles(theme);

  const [likes, setLikes] = useState<LikeWithProfile[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchLikes = useCallback(
    async (isRefreshing = false) => {
      if (!user) return;

      if (isRefreshing) {
        setRefreshing(true);
      } else if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      try {
        const result = await likesService.fetchLikes(
          user.id,
          isRefreshing ? 1 : page,
          20,
          searchTerm
        );
        if (isRefreshing) {
          setLikes(result.items);
        } else {
          setLikes((prev) =>
            page === 1 ? result.items : [...prev, ...result.items]
          );
        }
        setHasMore(result.hasMore);
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [user, page, searchTerm]
  );

  useEffect(() => {
    setPage(1);
    fetchLikes(true);
  }, [searchTerm, fetchLikes]);

  useEffect(() => {
    if (page > 1) {
      fetchLikes();
    }
  }, [page, fetchLikes]);

  const handleRefresh = () => {
    setPage(1);
    fetchLikes(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  const handleUnlike = async (likeId: string) => {
    try {
      await likesService.unlike(likeId);
      setLikes((prevLikes) => prevLikes.filter((like) => like.id !== likeId));
    } catch (error) {
      console.error("Failed to unlike:", error);
    }
  };

  if (loading) {
    return <LikesModalSkeleton />;
  }

  if (likes.length === 0) {
    return (
      <SocialEmptyState
        type="likes-given"
        searchTerm={searchTerm}
        isBusinessProfile={false}
        actionText="Discover businesses"
        onAction={onDiscoverPress}
      />
    );
  }

  return (
    <FlatList
      data={likes}
      renderItem={({ item }) => (
        <LikeCard like={item} onUnlike={handleUnlike} />
      )}
      keyExtractor={(item) => item.id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={
        loadingMore ? (
          <ActivityIndicator style={styles.footerLoadingContainer} />
        ) : null
      }
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    />
  );
};

export default LikesList;
