"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { PostFormData } from "@/lib/types/posts";
import { ActionResponse } from "@/lib/types/api";
import { deletePostMedia } from "@/lib/actions/shared/upload-post-media";
import { deleteCustomerPostMedia } from "@/lib/actions/shared/delete-customer-post-media";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";

/**
 * Create a new post
 */
export async function createPost(
  formData: PostFormData
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to create a post",
    };
  }

  // Get the user's business profile
  const { data: businessProfile, error: profileError } = await supabase
    .from(TABLES.BUSINESS_PROFILES)
    .select(
      `${COLUMNS.ID}, ${COLUMNS.CITY_SLUG}, ${COLUMNS.STATE_SLUG}, ${COLUMNS.LOCALITY_SLUG}, ${COLUMNS.PINCODE}, ${COLUMNS.LOGO_URL}`
    )
    .eq(COLUMNS.ID, user.id)
    .single();

  if (profileError || !businessProfile) {
    return {
      success: false,
      message: "Business profile not found",
      error: "You must have a business profile to create a post",
    };
  }

  // Prepare post data
  const postData = {
    [COLUMNS.BUSINESS_ID]: user.id,
    [COLUMNS.CONTENT]: formData.content,
    [COLUMNS.IMAGE_URL]: formData.image_url || null,
    [COLUMNS.CITY_SLUG]: businessProfile.city_slug,
    [COLUMNS.STATE_SLUG]: businessProfile.state_slug,
    [COLUMNS.LOCALITY_SLUG]: businessProfile.locality_slug,
    [COLUMNS.PINCODE]: businessProfile.pincode,
    product_ids: formData.product_ids || [],
    mentioned_business_ids: formData.mentioned_business_ids || [],
    author_avatar: businessProfile.logo_url,
  };

  // Insert the post
  const { data, error } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .insert(postData)
    .select()
    .single();

  if (error) {
    console.error("Error creating post:", error);
    return {
      success: false,
      message: "Failed to create post",
      error: error.message,
    };
  }

  // Revalidate the feed pages
  revalidatePath("/dashboard/business/feed");
  revalidatePath("/dashboard/customer/feed");

  return {
    success: true,
    message: "Post created successfully",
    data,
  };
}

/**
 * Update only the content of an existing post (for inline editing)
 * Handles both business and customer posts
 */
export async function updatePostContent(
  postId: string,
  content: string
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  // First, try to find the post in business_posts table
  const { data: businessPost, error: businessPostError } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .select(COLUMNS.ID)
    .eq(COLUMNS.ID, postId)
    .eq(COLUMNS.BUSINESS_ID, user.id)
    .single();

  // If not found in business_posts, try customer_posts table
  let existingPost = businessPost;
  let isBusinessPost = true;
  let postError = businessPostError;

  if (businessPostError) {
    const { data: customerPost, error: customerPostError } = await supabase
      .from(TABLES.CUSTOMER_POSTS)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, postId)
      .eq(COLUMNS.CUSTOMER_ID, user.id)
      .single();

    existingPost = customerPost;
    isBusinessPost = false;
    postError = customerPostError;
  }

  if (postError || !existingPost) {
    return {
      success: false,
      message: "Post not found",
      error:
        "The post does not exist or you do not have permission to update it",
    };
  }

  // Update only the content and timestamp
  const tableName = isBusinessPost
    ? TABLES.BUSINESS_POSTS
    : TABLES.CUSTOMER_POSTS;
  const { data, error } = await supabase
    .from(tableName)
    .update({
      [COLUMNS.CONTENT]: content.trim(),
      [COLUMNS.UPDATED_AT]: new Date().toISOString(),
    })
    .eq(COLUMNS.ID, postId)
    .select()
    .single();

  if (error) {
    console.error("Error updating post content:", error);
    return {
      success: false,
      message: "Failed to update post",
      error: error.message,
    };
  }

  // Revalidate the feed pages
  revalidatePath("/dashboard/business");
  revalidatePath("/dashboard/customer");
  revalidatePath("/dashboard/business/posts");

  return {
    success: true,
    message: "Post updated successfully",
    data,
  };
}

/**
 * Update only the product_ids of an existing post (for inline editing)
 * Only applies to business posts - customer posts don't have linked products
 */
export async function updatePostProducts(
  postId: string,
  productIds: string[]
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  // Check if the post exists and belongs to the user (only business posts have products)
  const { data: existingPost, error: postError } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .select(COLUMNS.ID)
    .eq(COLUMNS.ID, postId)
    .eq(COLUMNS.BUSINESS_ID, user.id)
    .single();

  if (postError || !existingPost) {
    return {
      success: false,
      message: "Post not found",
      error:
        "The post does not exist or you do not have permission to update it",
    };
  }

  // Update only the product_ids and timestamp
  const { data, error } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .update({
      product_ids: productIds,
      [COLUMNS.UPDATED_AT]: new Date().toISOString(),
    })
    .eq(COLUMNS.ID, postId)
    .select()
    .single();

  if (error) {
    console.error("Error updating post products:", error);
    return {
      success: false,
      message: "Failed to update post products",
      error: error.message,
    };
  }

  // Revalidate the feed pages
  revalidatePath("/dashboard/business/feed");
  revalidatePath("/dashboard/customer/feed");
  revalidatePath("/dashboard/business/posts");

  return {
    success: true,
    message: "Post products updated successfully",
    data,
  };
}

/**
 * Update an existing post (full update for form submissions)
 * Handles both business and customer posts
 */
export async function updatePost(
  postId: string,
  formData: PostFormData
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  // First, try to find the post in business_posts table
  const { data: businessPost, error: businessPostError } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .select(COLUMNS.ID)
    .eq(COLUMNS.ID, postId)
    .eq(COLUMNS.BUSINESS_ID, user.id)
    .single();

  // If not found in business_posts, try customer_posts table
  let existingPost = businessPost;
  let isBusinessPost = true;
  let postError = businessPostError;

  if (businessPostError) {
    const { data: customerPost, error: customerPostError } = await supabase
      .from(TABLES.CUSTOMER_POSTS)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, postId)
      .eq(COLUMNS.CUSTOMER_ID, user.id)
      .single();

    existingPost = customerPost;
    isBusinessPost = false;
    postError = customerPostError;
  }

  if (postError || !existingPost) {
    return {
      success: false,
      message: "Post not found",
      error:
        "The post does not exist or you do not have permission to update it",
    };
  }

  // Prepare update data based on post type
  const updateData = {
    [COLUMNS.CONTENT]: formData.content,
    [COLUMNS.IMAGE_URL]: formData.image_url || null,
    [COLUMNS.UPDATED_AT]: new Date().toISOString(),
    // Only include business-specific fields for business posts
    ...(isBusinessPost && {
      product_ids: formData.product_ids || [],
      mentioned_business_ids: formData.mentioned_business_ids || [],
    }),
  };

  // Update the post
  const tableName = isBusinessPost
    ? TABLES.BUSINESS_POSTS
    : TABLES.CUSTOMER_POSTS;
  const { data, error } = await supabase
    .from(tableName)
    .update(updateData)
    .eq(COLUMNS.ID, postId)
    .select()
    .single();

  if (error) {
    console.error("Error updating post:", error);
    return {
      success: false,
      message: "Failed to update post",
      error: error.message,
    };
  }

  // Revalidate the feed pages
  revalidatePath("/dashboard/business/feed");
  revalidatePath("/dashboard/customer/feed");
  revalidatePath("/dashboard/business/posts");

  return {
    success: true,
    message: "Post updated successfully",
    data,
  };
}

/**
 * Delete a post (handles both business and customer posts)
 */
export async function deletePost(postId: string): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to delete a post",
    };
  }

  // First, try to find the post in business_posts table
  const { data: businessPost, error: businessPostError } = await supabase
    .from(TABLES.BUSINESS_POSTS)
    .select(`${COLUMNS.ID}, ${COLUMNS.CREATED_AT}, ${COLUMNS.IMAGE_URL}`)
    .eq(COLUMNS.ID, postId)
    .eq(COLUMNS.BUSINESS_ID, user.id)
    .single();

  // If not found in business_posts, try customer_posts table
  let existingPost = businessPost;
  let isBusinessPost = true;
  let postError = businessPostError;

  if (businessPostError) {
    const { data: customerPost, error: customerPostError } = await supabase
      .from(TABLES.CUSTOMER_POSTS)
      .select(`${COLUMNS.ID}, ${COLUMNS.CREATED_AT}, ${COLUMNS.IMAGE_URL}`)
      .eq(COLUMNS.ID, postId)
      .eq(COLUMNS.CUSTOMER_ID, user.id)
      .single();

    existingPost = customerPost;
    isBusinessPost = false;
    postError = customerPostError;
  }

  if (postError || !existingPost) {
    return {
      success: false,
      message: "Post not found",
      error:
        "The post does not exist or you do not have permission to delete it",
    };
  }

  // First, attempt to delete the post folder from storage
  // This ensures we clean up any files that might exist, regardless of image_url status
  try {
    let mediaDeleteResult;
    if (isBusinessPost) {
      // Business posts are stored in the "business" bucket
      mediaDeleteResult = await deletePostMedia(
        user.id,
        postId,
        existingPost.created_at
      );
    } else {
      // Customer posts are stored in the "customers" bucket
      mediaDeleteResult = await deleteCustomerPostMedia(
        user.id,
        postId,
        existingPost.created_at
      );
    }

    if (!mediaDeleteResult.success && mediaDeleteResult.error) {
      console.error("Error deleting post media:", mediaDeleteResult.error);
      return {
        success: false,
        message: "Failed to delete post images",
        error: `Cannot delete post: ${mediaDeleteResult.error}`,
      };
    }
  } catch (mediaError) {
    console.error("Error deleting post media:", mediaError);
    return {
      success: false,
      message: "Failed to delete post images",
      error: "Cannot delete post: Failed to clean up associated images",
    };
  }

  // Only delete the post after successful media deletion
  const tableName = isBusinessPost
    ? TABLES.BUSINESS_POSTS
    : TABLES.CUSTOMER_POSTS;
  const { error } = await supabase
    .from(tableName)
    .delete()
    .eq(COLUMNS.ID, postId);

  if (error) {
    console.error("Error deleting post:", error);
    return {
      success: false,
      message: "Failed to delete post",
      error: error.message,
    };
  }

  // Revalidate the feed pages
  revalidatePath("/dashboard/business");
  revalidatePath("/dashboard/customer");
  revalidatePath("/dashboard/business/posts");

  return {
    success: true,
    message: "Post deleted successfully",
  };
}
