"use client";

import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Upload, Camera } from "lucide-react";
import { toast } from "sonner";
import QRScanner from "./QRScanner";
import { Html5Qrcode } from "html5-qrcode";
import { validateQRCodeForUser } from "@/lib/utils/qrCodeUtils";

interface QRScannerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onScanSuccess: (_businessSlug: string) => void;
}

const QRScannerModal: React.FC<QRScannerModalProps> = ({
  isOpen,
  onClose,
  onScanSuccess,
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [scanMode, setScanMode] = useState<"camera" | "upload">("camera");

  const handleScanSuccess = useCallback(
    (businessSlug: string) => {
      setIsProcessing(true);

      // Add a small delay to show success state
      setTimeout(() => {
        toast.success("QR code scanned successfully!");
        onScanSuccess(businessSlug);
        onClose();
        setIsProcessing(false);
      }, 500);
    },
    [onScanSuccess, onClose]
  );

  const handleScanError = useCallback((error: string) => {
    console.error("QR scan error:", error);
    toast.error(error);
  }, []);

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Check if file is an image
      if (!file.type.startsWith("image/")) {
        toast.error("Please select an image file");
        return;
      }

      setIsProcessing(true);

      try {
        let html5QrCode: Html5Qrcode | undefined; // Declare outside to ensure scope for finally
        try {
          html5QrCode = new Html5Qrcode("qr-file-scanner-region");
          const qrCodeResult = await html5QrCode.scanFile(file);
          console.log("Html5Qrcode scan result:", qrCodeResult);

          // Validate the QR code
          const validation = validateQRCodeForUser(qrCodeResult);
          console.log("QR code validation result:", validation);

          if (!validation.isValid) {
            const errorMessage = validation.error || "Invalid QR code";
            toast.error(errorMessage);
            setIsProcessing(false);
            return;
          }

          // Extract business slug and call success callback
          const businessSlug = validation.businessSlug!;
          handleScanSuccess(businessSlug);
        } catch (err: any) {
          console.error("QR code scan from image failed:", err);
          let errorMessage = "Failed to process image";
          if (typeof err === "string") {
            if (err.includes("QR code not found")) {
              errorMessage =
                "No Dukancard QR code found in the image. Please try another image.";
            } else if (err.includes("no multiformat readers")) {
              errorMessage =
                "No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.";
            } else if (err.includes("no multiformat readers")) {
              errorMessage =
                "No QR code found in the image. Please ensure it's a clear image of a Dukancard QR code.";
            } else if (err.includes("Image parse error")) {
              errorMessage =
                "Could not read the image file. Please ensure it's a valid image.";
            } else {
              errorMessage = err; // Fallback to raw error message if it\'s a string
            }
          } else if (err instanceof Error) {
            errorMessage = err.message;
          }
          toast.error(errorMessage);
          setIsProcessing(false);
        } finally {
          if (html5QrCode) {
            try {
              html5QrCode.clear();
            } catch (e: unknown) {
              console.error("Error clearing html5QrCode", e);
            }
          }
        }
      } catch (_error) {
        console.error("Outer catch: Failed to process image", _error);
        toast.error("Failed to process image");
        setIsProcessing(false);
      }

      // Reset file input
      event.target.value = "";
    },
    [handleScanSuccess]
  );

  const handleClose = useCallback(() => {
    if (!isProcessing) {
      onClose();
    }
  }, [isProcessing, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md w-full max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg font-semibold">
            Scan QR Code
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Mode Toggle */}
          <div className="flex rounded-lg bg-muted p-1">
            <button
              onClick={() => setScanMode("camera")}
              disabled={isProcessing}
              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                scanMode === "camera"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Camera className="h-4 w-4" />
              Camera
            </button>
            <button
              onClick={() => setScanMode("upload")}
              disabled={isProcessing}
              className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                scanMode === "upload"
                  ? "bg-background text-foreground shadow-sm"
                  : "text-muted-foreground hover:text-foreground"
              }`}
            >
              <Upload className="h-4 w-4" />
              Upload
            </button>
          </div>

          {/* Scanner Content */}
          <div className="relative">
            {scanMode === "camera" ? (
              <QRScanner
                onScanSuccess={handleScanSuccess}
                onScanError={handleScanError}
                onClose={handleClose}
                className="min-h-[300px]"
              />
            ) : (
              <div className="min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-8">
                <Upload className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  Upload QR Code Image
                </h3>
                <p className="text-muted-foreground text-center mb-4">
                  Select an image containing a Dukancard QR code
                </p>
                <label htmlFor="qr-upload" className="cursor-pointer">
                  <Button asChild disabled={isProcessing}>
                    <span>
                      {isProcessing ? "Processing..." : "Choose Image"}
                    </span>
                  </Button>
                  <input
                    id="qr-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleFileUpload}
                    disabled={isProcessing}
                    className="hidden"
                  />
                </label>
              </div>
            )}
          </div>

          {/* Hidden div for Html5Qrcode file scanning */}
          <div id="qr-file-scanner-region" style={{ display: "none" }} />

          {/* Instructions */}
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              {scanMode === "camera"
                ? "Position the QR code within the camera frame"
                : "Upload an image containing a Dukancard QR code"}
            </p>
            <p className="text-xs text-muted-foreground">
              Only Dukancard business QR codes are supported
            </p>
          </div>

          {/* Processing Overlay */}
          {isProcessing && (
            <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-50 rounded-lg">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-foreground font-medium">
                  Processing QR code...
                </p>
                <p className="text-sm text-muted-foreground">Please wait</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QRScannerModal;
