/**
 * Navigation Types
 * Types related to navigation and routing
 */

export interface NavigationParams {
  [key: string]: any;
}

export interface RouteParams {
  businessSlug?: string;
  postId?: string;
  productId?: string;
}

export interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export interface RootStackParamList {
  Home: undefined;
  Profile: { userId: string };
  Business: { businessSlug: string };
  Post: { postId: string };
  Product: { productId: string };
  Following: undefined;
  Likes: undefined;
  Reviews: undefined;
  [key: string]: any;
}
