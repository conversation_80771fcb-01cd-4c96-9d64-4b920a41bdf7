/**
 * Integration tests for end-to-end post deletion workflow in Next.js
 * Tests the complete flow from UI interaction to storage cleanup
 */

import { deletePost } from "@/lib/actions/posts/crud";
import { deletePostMedia } from "@/lib/actions/shared/upload-post-media";
import { deleteCustomerPostMedia } from "@/lib/actions/shared/delete-customer-post-media";

// Mock the dependencies
jest.mock("@/utils/supabase/server", () => ({
  createClient: jest.fn(),
}));
jest.mock("@/lib/actions/shared/upload-post-media");
jest.mock("@/lib/actions/shared/delete-customer-post-media");
jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(),
};

const mockDeletePostMedia = deletePostMedia as jest.MockedFunction<typeof deletePostMedia>;
const mockDeleteCustomerPostMedia = deleteCustomerPostMedia as jest.MockedFunction<typeof deleteCustomerPostMedia>;

// Import after mocking
const { createClient } = require("@/utils/supabase/server");
const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;

describe("Post Deletion Workflow Integration", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateClient.mockResolvedValue(mockSupabase as any);
  });

  describe("Complete Business Post Deletion Flow", () => {
    const mockUser = { id: "user-123" };
    const mockBusinessPost = {
      id: "business-post-456",
      user_id: "user-123",
      post_source: "business",
      created_at: "2024-01-01T00:00:00Z",
      content: "Test business post",
      image_url: "https://example.com/image.jpg",
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should complete full business post deletion workflow", async () => {
      // Mock database operations
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockBusinessPost,
        error: null,
      });
      const mockDelete = jest.fn().mockReturnThis();
      const mockDeleteEq = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
        delete: mockDelete,
      });

      mockSelect.mockReturnValue({
        eq: mockEq,
      });

      mockEq.mockReturnValue({
        eq: mockEq,
        single: mockSingle,
      });

      mockDelete.mockReturnValue({
        eq: mockDeleteEq,
      });

      // Mock successful media deletion
      mockDeletePostMedia.mockResolvedValue({
        success: true,
      });

      // Execute the deletion
      const result = await deletePost("business-post-456");

      // Verify the complete workflow
      expect(result.success).toBe(true);

      // Verify authentication check
      expect(mockSupabase.auth.getUser).toHaveBeenCalled();

      // Verify post retrieval
      expect(mockSupabase.from).toHaveBeenCalledWith("business_posts");
      expect(mockSelect).toHaveBeenCalled();
      expect(mockEq).toHaveBeenCalledWith("id", "business-post-456");

      // Verify media deletion (business bucket)
      expect(mockDeletePostMedia).toHaveBeenCalledWith(
        "user-123",
        "business-post-456",
        "2024-01-01T00:00:00Z"
      );
      expect(mockDeleteCustomerPostMedia).not.toHaveBeenCalled();

      // Verify database deletion
      expect(mockDelete).toHaveBeenCalled();
      expect(mockDeleteEq).toHaveBeenCalledWith("id", "business-post-456");
    });

    it("should rollback on media deletion failure", async () => {
      // Mock database operations
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockBusinessPost,
        error: null,
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      });

      mockSelect.mockReturnValue({
        eq: mockEq,
      });

      mockEq.mockReturnValue({
        eq: mockEq,
        single: mockSingle,
      });

      // Mock failed media deletion
      mockDeletePostMedia.mockResolvedValue({
        success: false,
        error: "Storage deletion failed",
      });

      const result = await deletePost("business-post-456");

      // Verify failure handling
      expect(result.success).toBe(false);
      expect(result.error).toContain("Storage deletion failed");

      // Verify that database deletion was not attempted
      expect(mockSupabase.from).toHaveBeenCalledTimes(1); // Only for retrieval
    });
  });

  describe("Complete Customer Post Deletion Flow", () => {
    const mockUser = { id: "user-789" };
    const mockCustomerPost = {
      id: "customer-post-123",
      user_id: "user-789",
      post_source: "customer",
      created_at: "2024-02-01T00:00:00Z",
      content: "Test customer post",
      image_url: "https://example.com/customer-image.jpg",
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should complete full customer post deletion workflow", async () => {
      // Mock database operations
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn()
        .mockResolvedValueOnce({ data: null, error: { message: "Not found" } }) // Business post not found
        .mockResolvedValueOnce({ data: mockCustomerPost, error: null }); // Customer post found
      const mockDelete = jest.fn().mockReturnThis();
      const mockDeleteEq = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
        delete: mockDelete,
      });

      mockSelect.mockReturnValue({
        eq: mockEq,
      });

      mockEq.mockReturnValue({
        eq: mockEq,
        single: mockSingle,
      });

      mockDelete.mockReturnValue({
        eq: mockDeleteEq,
      });

      // Mock successful media deletion
      mockDeleteCustomerPostMedia.mockResolvedValue({
        success: true,
      });

      // Execute the deletion
      const result = await deletePost("customer-post-123");

      // Verify the complete workflow
      expect(result.success).toBe(true);

      // Verify authentication check
      expect(mockSupabase.auth.getUser).toHaveBeenCalled();

      // Verify post retrieval (both business and customer tables)
      expect(mockSupabase.from).toHaveBeenCalledWith("business_posts");
      expect(mockSupabase.from).toHaveBeenCalledWith("customer_posts");

      // Verify media deletion (customers bucket)
      expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(
        "user-789",
        "customer-post-123",
        "2024-02-01T00:00:00Z"
      );
      expect(mockDeletePostMedia).not.toHaveBeenCalled();

      // Verify database deletion
      expect(mockDelete).toHaveBeenCalled();
      expect(mockDeleteEq).toHaveBeenCalledWith("id", "customer-post-123");
    });
  });

  describe("Cross-Platform Consistency", () => {
    it("should use same deletion logic for both platforms", async () => {
      const mockUser = { id: "user-123" };
      const mockPost = {
        id: "test-post-456",
        user_id: "user-123",
        post_source: "business",
        created_at: "2024-01-01T00:00:00Z",
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // Mock database operations
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockPost,
        error: null,
      });
      const mockDelete = jest.fn().mockReturnThis();
      const mockDeleteEq = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
        delete: mockDelete,
      });

      mockSelect.mockReturnValue({
        eq: mockEq,
      });

      mockEq.mockReturnValue({
        eq: mockEq,
        single: mockSingle,
      });

      mockDelete.mockReturnValue({
        eq: mockDeleteEq,
      });

      mockDeletePostMedia.mockResolvedValue({ success: true });

      const result = await deletePost("test-post-456");

      expect(result.success).toBe(true);

      // Verify that the same authentication, retrieval, and deletion patterns
      // are used that would be consistent with React Native implementation
      expect(mockSupabase.auth.getUser).toHaveBeenCalled();
      expect(mockSupabase.from).toHaveBeenCalledWith("business_posts");
      expect(mockDeletePostMedia).toHaveBeenCalledWith(
        "user-123",
        "test-post-456",
        "2024-01-01T00:00:00Z"
      );
    });
  });

  describe("Error Recovery and Cleanup", () => {
    it("should handle partial failures gracefully", async () => {
      const mockUser = { id: "user-123" };
      const mockPost = {
        id: "test-post-789",
        user_id: "user-123",
        post_source: "business",
        created_at: "2024-01-01T00:00:00Z",
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      // Mock successful post retrieval
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({
        data: mockPost,
        error: null,
      });

      mockSupabase.from.mockReturnValue({
        select: mockSelect,
      });

      mockSelect.mockReturnValue({
        eq: mockEq,
      });

      mockEq.mockReturnValue({
        eq: mockEq,
        single: mockSingle,
      });

      // Mock media deletion failure
      mockDeletePostMedia.mockResolvedValue({
        success: false,
        error: "Network timeout",
      });

      const result = await deletePost("test-post-789");

      // Verify that the operation fails safely
      expect(result.success).toBe(false);
      expect(result.error).toContain("Network timeout");

      // Verify that no database deletion was attempted
      // (only one call to mockSupabase.from for retrieval)
      expect(mockSupabase.from).toHaveBeenCalledTimes(1);
    });
  });
});
