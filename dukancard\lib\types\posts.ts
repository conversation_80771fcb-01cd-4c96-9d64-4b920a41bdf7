/**
 * Types for business posts, customer posts, and feed functionality
 */

import { z } from "zod";
import { Tables } from "@/types/supabase";

type ProductsServices = Tables<"products_services">;
type BusinessPosts = Tables<"business_posts">;
type CustomerPosts = Tables<"customer_posts">;

// Profile types enum (kept for future compatibility)
export const ProfileType = {
  BUSINESS: "business",
  CUSTOMER: "customer",
} as const;

export type ProfileTypeValue = (typeof ProfileType)[keyof typeof ProfileType];

// Business post schema for validation
export const postSchema = z.object({
  id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
  content: z
    .string()
    .min(1, { message: "Post content is required" })
    .max(2000, { message: "Post content cannot exceed 2000 characters" }),
  image_url: z
    .union([
      z.string().url({ message: "Invalid image URL" }),
      z.literal(""),
      z.null(),
      z.undefined(),
    ])
    .optional()
    .nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  city_slug: z.string().optional(),
  state_slug: z.string().optional(),
  locality_slug: z.string().optional(),
  pincode: z.string().optional(),
  product_ids: z
    .array(z.string().uuid())
    .max(5, { message: "You can link up to 5 products per post" })
    .default([]),
  mentioned_business_ids: z
    .array(z.string().uuid())
    .max(10, { message: "You can mention up to 10 businesses per post" })
    .default([]),
});

// Type for post data from form
export type PostFormData = z.infer<typeof postSchema>;

// Alias for compatibility with existing code
export type PostData = BusinessPosts;

// Product data type for compatibility
export type ProductData = ProductsServices;

// Type for post data with business profile information
export interface PostWithBusinessProfile extends BusinessPosts {
  customer_id?: string; // For customer posts
  post_source: "business" | "customer";
  business_profiles?: {
    id: string;
    business_name: string | null;
    logo_url: string | null;
    business_slug: string | null;
    phone: string | null;
    whatsapp_number: string | null;
    city: string | null;
    state: string | null;
  };
}

// Type for post data with linked products
export interface PostWithProducts extends BusinessPosts {
  linked_products?: ProductsServices[];
}

// Type for feed items (currently only business posts)
export type FeedItem = PostWithBusinessProfile & Partial<PostWithProducts>;

// Type for feed filter options
export type FeedFilterType =
  | "smart"
  | "subscribed"
  | "locality"
  | "pincode"
  | "city"
  | "state"
  | "all";

// Type for feed query parameters
export interface FeedQueryParams {
  filter?: FeedFilterType;
  page?: number;
  limit?: number;
  city_slug?: string;
  state_slug?: string;
  locality_slug?: string;
  pincode?: string;
}

// ============================================================================
// CUSTOMER POSTS TYPES
// ============================================================================

// Customer post schema for validation
export const customerPostSchema = z.object({
  id: z.string().uuid().optional(),
  customer_id: z.string().uuid().optional(),
  content: z
    .string()
    .min(1, { message: "Post content is required" })
    .max(2000, { message: "Post content cannot exceed 2000 characters" }),
  image_url: z
    .union([
      z.string().url({ message: "Invalid image URL" }),
      z.literal(""),
      z.null(),
      z.undefined(),
    ])
    .optional()
    .nullable(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
  city_slug: z.string().optional(),
  state_slug: z.string().optional(),
  locality_slug: z.string().optional(),
  pincode: z.string().optional(),
  mentioned_business_ids: z
    .array(z.string().uuid())
    .max(10, { message: "You can mention up to 10 businesses per post" })
    .default([]),
});

// Type for customer post data from form
export type CustomerPostFormData = z.infer<typeof customerPostSchema>;

// Alias for compatibility with existing code
export type CustomerPostData = CustomerPosts;

// Type for customer post data with customer profile information
export interface CustomerPostWithProfile extends CustomerPosts {
  customer_profiles_public?: {
    id: string;
    name: string | null;
    avatar_url: string | null;
    city: string | null;
    state: string | null;
    locality: string | null;
  };
  // Legacy support - will be removed
  customer_profiles?: {
    id: string;
    name: string | null;
    avatar_url: string | null;
    city: string | null;
    state: string | null;
    locality: string | null;
  };
}

// Type for customer post data with mentioned businesses
export interface CustomerPostWithBusinesses extends CustomerPosts {
  mentioned_businesses?: {
    id: string;
    business_name: string | null;
    logo_url: string | null;
    business_slug: string | null;
    city: string | null;
    state: string | null;
  }[];
}

// Combined type for customer posts with all related data
export type CustomerFeedItem = CustomerPostWithProfile &
  Partial<CustomerPostWithBusinesses>;
