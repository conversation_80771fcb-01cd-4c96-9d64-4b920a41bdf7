"use server";

import {
  subscriptionsService,
  SubscriptionsResult,
} from "@/lib/services/socialService";

// Re-export types for compatibility
export interface SubscriberProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
  type: "business" | "customer";
}

export interface SubscriberWithProfile {
  id: string;
  profile: SubscriberProfileData | null;
}

export interface SubscribersResult {
  items: SubscriberWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Define types for businesses that this business follows
export interface BusinessFollowingData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

export interface BusinessFollowingWithProfile {
  id: string;
  business_profiles: BusinessFollowingData | null;
}

export interface BusinessFollowingResult {
  items: BusinessFollowingWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch subscribers to a business (both customers and other businesses)
 * Note: Search functionality removed as per requirements
 */
export async function fetchBusinessSubscribers(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<SubscribersResult> {
  try {
    console.log(
      `[fetchBusinessSubscribers] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}`
    );
    const result = await subscriptionsService.fetchBusinessFollowers(
      businessId,
      page,
      limit
    );
    console.log(
      `[fetchBusinessSubscribers] Received ${result.items.length} items, totalCount: ${result.totalCount}`
    );
    // Transform FollowersResult to SubscribersResult for compatibility
    return {
      items: result.items.map((item) => ({
        id: item.id,
        profile: item.profile,
      })),
      totalCount: result.totalCount,
      hasMore: result.hasMore,
      currentPage: result.currentPage,
    };
  } catch (error) {
    console.error("Error in fetchBusinessSubscribers:", error);
    throw error;
  }
}

/**
 * Fetch businesses that this business follows
 */
export async function fetchBusinessFollowing(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<SubscriptionsResult> {
  try {
    console.log(
      `[fetchBusinessFollowing] Fetching for businessId: ${businessId}, page: ${page}, limit: ${limit}, searchTerm: ${searchTerm}`
    );
    const result = await subscriptionsService.fetchSubscriptions(
      businessId,
      page,
      limit,
      searchTerm
    );
    console.log(
      `[fetchBusinessFollowing] Received ${result.items.length} items, totalCount: ${result.totalCount}`
    );
    return result;
  } catch (error) {
    console.error("Error in fetchBusinessFollowing:", error);
    throw error;
  }
}
