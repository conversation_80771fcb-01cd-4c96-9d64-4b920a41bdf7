/**
 * Unit tests for lib/actions/shared/upload-post-media.ts
 * Tests the deletePostMedia function with fixed client usage
 */

import { deletePostMedia } from "@/lib/actions/shared/upload-post-media";
import { createClient } from "@/utils/supabase/server";

// Mock the Supabase client
const mockSupabase = {
  storage: {
    from: jest.fn(),
  },
};

jest.mock("@/utils/supabase/server", () => ({
  createClient: jest.fn(() => Promise.resolve(mockSupabase)),
}));

describe("lib/actions/shared/upload-post-media.ts", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("deletePostMedia function", () => {
    it("should delete media from business bucket successfully", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      const result = await deletePostMedia(
        "user-123",
        "post-456",
        "2024-01-01T00:00:00Z"
      );

      expect(result.success).toBe(true);
      expect(mockSupabase.storage.from).toHaveBeenCalledWith("business");
      expect(mockRemove).toHaveBeenCalledWith([
        "user-123/posts/post-456/2024-01-01T00:00:00Z",
      ]);
    });

    it("should handle storage deletion errors", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: { message: "Storage error" },
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      const result = await deletePostMedia(
        "user-123",
        "post-456",
        "2024-01-01T00:00:00Z"
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe("Storage error");
      expect(mockSupabase.storage.from).toHaveBeenCalledWith("business");
    });

    it("should handle client creation errors", async () => {
      const mockCreateClient = createClient as jest.MockedFunction<
        typeof createClient
      >;
      mockCreateClient.mockRejectedValue(new Error("Client creation failed"));

      const result = await deletePostMedia(
        "user-123",
        "post-456",
        "2024-01-01T00:00:00Z"
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe(
        "An unexpected error occurred while deleting post folder."
      );
    });

    it("should use regular client (not admin client)", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      await deletePostMedia("user-123", "post-456", "2024-01-01T00:00:00Z");

      // Verify that createClient was called (regular client)
      expect(createClient).toHaveBeenCalled();
      // Verify that the storage operation was performed on business bucket
      expect(mockSupabase.storage.from).toHaveBeenCalledWith("business");
    });

    it("should construct correct file paths", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      await deletePostMedia("user-456", "post-123", "2024-02-15T10:30:00Z");

      expect(mockRemove).toHaveBeenCalledWith([
        "user-456/posts/post-123/2024-02-15T10:30:00Z",
      ]);
    });

    it("should handle unexpected errors gracefully", async () => {
      mockSupabase.storage.from.mockImplementation(() => {
        throw new Error("Unexpected storage error");
      });

      const result = await deletePostMedia(
        "user-123",
        "post-456",
        "2024-01-01T00:00:00Z"
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain("Unexpected storage error");
    });

    it("should handle empty parameters", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      const result = await deletePostMedia("", "", "");

      expect(result.success).toBe(true);
      expect(mockRemove).toHaveBeenCalledWith(["/posts//"]);
    });

    it("should target business bucket specifically", async () => {
      const mockRemove = jest.fn().mockResolvedValue({
        data: null,
        error: null,
      });

      mockSupabase.storage.from.mockReturnValue({
        remove: mockRemove,
      });

      await deletePostMedia("user-123", "post-456", "2024-01-01T00:00:00Z");

      // Verify it specifically targets the business bucket
      expect(mockSupabase.storage.from).toHaveBeenCalledWith("business");
      expect(mockSupabase.storage.from).not.toHaveBeenCalledWith("customers");
    });
  });
});
