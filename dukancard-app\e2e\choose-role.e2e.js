const { device, expect, element, by, waitFor } = require('detox');

/**
 * E2E Tests for Choose Role Screen & Customer Profile Creation
 *
 * COMPREHENSIVE TESTING APPROACH:
 * ✅ Complete customer profile creation journey
 * ✅ Authentication flow testing
 * ✅ Role selection and navigation
 * ✅ Form validation and user experience testing
 * ✅ Error handling for network failures, validation errors
 * ✅ Mobile-specific scenarios: GPS, camera, permissions
 * ✅ Native interactions and gestures
 *
 * This test suite covers:
 * - Authentication → Choose Role → Profile Creation → Dashboard
 * - Complete profile form with address, GPS, and image upload
 * - Error handling for network failures, validation errors
 * - Edge cases: GPS unavailable, camera permissions, form validation
 * - Mobile-specific interactions: pull-to-refresh, native modals
 */

describe('Choose Role Screen & Customer Profile Creation - E2E', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Authentication and Role Selection Flow', () => {
    it('should navigate through complete authentication and role selection', async () => {
      // Step 1: Should start on login screen for unauthenticated user
      await expect(element(by.text('Welcome to Dukancard'))).toBeVisible();
      
      // Step 2: Complete email input
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.text('Continue')).tap();
      
      // Step 3: Should show OTP verification screen
      await waitFor(element(by.text('Enter Verification Code')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Step 4: Enter OTP (mock verification)
      await element(by.id('otp-input')).typeText('123456');
      await element(by.text('Verify')).tap();
      
      // Step 5: Should navigate to choose role screen
      await waitFor(element(by.text('Choose Your Role')))
        .toBeVisible()
        .withTimeout(15000);
      
      // Step 6: Verify role selection buttons are visible
      await expect(element(by.text('As a Customer'))).toBeVisible();
      await expect(element(by.text('As a Business'))).toBeVisible();
    });

    it('should handle customer role selection and profile creation', async () => {
      // Navigate to choose role screen (assuming authenticated state)
      await element(by.id('choose-role-screen')).tap();
      
      // Step 1: Select customer role
      await element(by.text('As a Customer')).tap();
      
      // Step 2: Should navigate to customer profile completion screen
      await waitFor(element(by.text('Complete Your Profile')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Step 3: Fill out basic profile information
      await element(by.id('name-input')).typeText('John Doe');
      
      // Step 4: Fill out address information
      await element(by.id('address-input')).typeText('123 Main Street');
      await element(by.id('pincode-input')).typeText('110001');
      
      // Step 5: Submit profile
      await element(by.text('Save Profile')).tap();
      
      // Step 6: Should navigate to customer dashboard
      await waitFor(element(by.text('Customer Dashboard')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle business role selection', async () => {
      // Navigate to choose role screen
      await element(by.id('choose-role-screen')).tap();
      
      // Step 1: Select business role
      await element(by.text('As a Business')).tap();
      
      // Step 2: Should navigate to business onboarding
      await waitFor(element(by.text('Business Onboarding')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });

  describe('Customer Profile Form Validation', () => {
    beforeEach(async () => {
      // Navigate to profile completion screen
      await element(by.id('choose-role-screen')).tap();
      await element(by.text('As a Customer')).tap();
      await waitFor(element(by.text('Complete Your Profile')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should validate required fields', async () => {
      // Try to submit without filling required fields
      await element(by.text('Save Profile')).tap();
      
      // Should show validation errors
      await expect(element(by.text('Name is required'))).toBeVisible();
    });

    it('should validate pincode format', async () => {
      await element(by.id('name-input')).typeText('John Doe');
      await element(by.id('pincode-input')).typeText('12345'); // Invalid 5-digit pincode
      
      await element(by.text('Save Profile')).tap();
      
      // Should show pincode validation error
      await expect(element(by.text('Must be a valid 6-digit pincode'))).toBeVisible();
    });

    it('should validate name length limits', async () => {
      // Test name too long (over 100 characters)
      const longName = 'A'.repeat(101);
      await element(by.id('name-input')).typeText(longName);
      
      await element(by.text('Save Profile')).tap();
      
      await expect(element(by.text('Name is too long'))).toBeVisible();
    });

    it('should handle special characters in form fields', async () => {
      // Test special characters in name
      await element(by.id('name-input')).typeText('José María O\'Connor-Smith');
      await element(by.id('address-input')).typeText('123 Main St. #4B');
      await element(by.id('pincode-input')).typeText('110001');
      
      await element(by.text('Save Profile')).tap();
      
      // Should handle special characters successfully
      await waitFor(element(by.text('Profile updated successfully')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });

  describe('Mobile-Specific Features', () => {
    beforeEach(async () => {
      // Navigate to profile completion screen
      await element(by.id('choose-role-screen')).tap();
      await element(by.text('As a Customer')).tap();
      await waitFor(element(by.text('Complete Your Profile')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle GPS location access', async () => {
      // Test GPS location button
      await element(by.id('gps-location-button')).tap();
      
      // Should request location permission (mock permission granted)
      await waitFor(element(by.text('Location access granted')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Should auto-populate address fields
      await expect(element(by.id('address-input'))).toHaveText('Auto-populated address');
    });

    it('should handle GPS permission denied', async () => {
      // Test GPS location button with permission denied
      await element(by.id('gps-location-button')).tap();
      
      // Should show permission denied message
      await waitFor(element(by.text('Location permission denied')))
        .toBeVisible()
        .withTimeout(5000);
      
      // Should allow manual address entry
      await element(by.id('address-input')).typeText('123 Manual Street');
      await expect(element(by.id('address-input'))).toHaveText('123 Manual Street');
    });

    it('should handle camera access for profile picture', async () => {
      // Test camera button for profile picture
      await element(by.id('camera-button')).tap();
      
      // Should show camera options modal
      await expect(element(by.text('Select Photo'))).toBeVisible();
      await expect(element(by.text('Take Photo'))).toBeVisible();
      await expect(element(by.text('Choose from Gallery'))).toBeVisible();
      
      // Select take photo option
      await element(by.text('Take Photo')).tap();
      
      // Should handle camera permission and capture
      await waitFor(element(by.text('Photo captured successfully')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle camera permission denied', async () => {
      // Test camera button with permission denied
      await element(by.id('camera-button')).tap();
      await element(by.text('Take Photo')).tap();
      
      // Should show permission denied message
      await waitFor(element(by.text('Camera permission denied')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Error Handling and Network Scenarios', () => {
    beforeEach(async () => {
      // Navigate to profile completion screen
      await element(by.id('choose-role-screen')).tap();
      await element(by.text('As a Customer')).tap();
      await waitFor(element(by.text('Complete Your Profile')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle network failure during profile creation', async () => {
      await element(by.id('name-input')).typeText('John Doe');
      await element(by.id('address-input')).typeText('123 Main Street');
      await element(by.id('pincode-input')).typeText('110001');
      
      // Simulate network failure during save
      await element(by.text('Save Profile')).tap();
      
      // Should show network error message
      await waitFor(element(by.text('Network error occurred')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Should allow retry
      await expect(element(by.text('Retry'))).toBeVisible();
    });

    it('should handle server errors gracefully', async () => {
      await element(by.id('name-input')).typeText('John Doe');
      await element(by.id('pincode-input')).typeText('110001');
      
      await element(by.text('Save Profile')).tap();
      
      // Should handle server errors
      await waitFor(element(by.text('An unexpected error occurred')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should provide proper loading states', async () => {
      await element(by.id('name-input')).typeText('John Doe');
      await element(by.id('pincode-input')).typeText('110001');
      
      await element(by.text('Save Profile')).tap();
      
      // Should show loading state
      await expect(element(by.id('loading-indicator'))).toBeVisible();
      
      // Should complete and show success
      await waitFor(element(by.text('Profile updated successfully')))
        .toBeVisible()
        .withTimeout(15000);
    });
  });

  describe('Navigation and User Experience', () => {
    it('should handle back navigation correctly', async () => {
      // Navigate to choose role
      await element(by.id('choose-role-screen')).tap();
      
      // Go back using device back button
      await device.pressBack();
      
      // Should return to previous screen
      await expect(element(by.text('Welcome to Dukancard'))).toBeVisible();
    });

    it('should handle app backgrounding and foregrounding', async () => {
      // Navigate to profile screen
      await element(by.id('choose-role-screen')).tap();
      await element(by.text('As a Customer')).tap();
      
      // Fill some data
      await element(by.id('name-input')).typeText('John Doe');
      
      // Background and foreground app
      await device.sendToHome();
      await device.launchApp();
      
      // Should maintain form state or handle appropriately
      await expect(element(by.text('Complete Your Profile'))).toBeVisible();
    });

    it('should handle screen rotation', async () => {
      // Navigate to profile screen
      await element(by.id('choose-role-screen')).tap();
      await element(by.text('As a Customer')).tap();
      
      // Rotate device
      await device.setOrientation('landscape');
      
      // Should maintain layout and functionality
      await expect(element(by.text('Complete Your Profile'))).toBeVisible();
      await expect(element(by.id('name-input'))).toBeVisible();
      
      // Rotate back
      await device.setOrientation('portrait');
      await expect(element(by.text('Complete Your Profile'))).toBeVisible();
    });
  });
});
