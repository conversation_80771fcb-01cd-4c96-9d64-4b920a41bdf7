import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { deletePostMedia } from "@/lib/actions/shared/upload-post-media";
import { deleteCustomerPostMedia } from "@/lib/actions/shared/delete-customer-post-media";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import {
  createPost,
  deletePost,
  updatePostContent,
  updatePostProducts,
  updatePost,
} from "@/lib/actions/posts/crud";

// Mock dependencies
jest.mock("@/utils/supabase/server");
jest.mock("next/cache");
jest.mock("@/lib/actions/shared/upload-post-media");
jest.mock("@/lib/actions/shared/delete-customer-post-media");

const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(),
};

const mockCreateClient = createClient as jest.MockedFunction<
  typeof createClient
>;
const mockRevalidatePath = revalidatePath as jest.MockedFunction<
  typeof revalidatePath
>;
const mockDeletePostMedia = deletePostMedia as jest.MockedFunction<
  typeof deletePostMedia
>;
const mockDeleteCustomerPostMedia =
  deleteCustomerPostMedia as jest.MockedFunction<
    typeof deleteCustomerPostMedia
  >;

describe("Post CRUD Operations", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateClient.mockResolvedValue(mockSupabase as any);
  });

  describe("deletePost", () => {
    const mockUser = { id: "user-123" };
    const mockPostId = "post-456";

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should delete a business post successfully", async () => {
      const mockBusinessPost = {
        id: mockPostId,
        created_at: "2024-01-01T00:00:00Z",
        image_url: "https://example.com/image.jpg",
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockBusinessPost,
          error: null,
        }),
        delete: jest.fn().mockReturnThis(),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);
      mockDeletePostMedia.mockResolvedValue({ success: true });

      const result = await deletePost(mockPostId);

      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.BUSINESS_POSTS);
      expect(mockFromChain.select).toHaveBeenCalledWith(
        `${COLUMNS.ID}, ${COLUMNS.CREATED_AT}, ${COLUMNS.IMAGE_URL}`
      );
      expect(mockFromChain.eq).toHaveBeenCalledWith(COLUMNS.ID, mockPostId);
      expect(mockFromChain.eq).toHaveBeenCalledWith(
        COLUMNS.BUSINESS_ID,
        mockUser.id
      );
      expect(mockDeletePostMedia).toHaveBeenCalledWith(
        mockUser.id,
        mockPostId,
        mockBusinessPost.created_at
      );
      expect(result.success).toBe(true);
      expect(result.message).toBe("Post deleted successfully");
    });

    it("should delete a customer post when business post not found", async () => {
      const mockCustomerPost = {
        id: mockPostId,
        created_at: "2024-01-01T00:00:00Z",
        image_url: "https://example.com/image.jpg",
      };

      let callCount = 0;
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // First call for business_posts - return error
            return Promise.resolve({
              data: null,
              error: { message: "Not found" },
            });
          } else {
            // Second call for customer_posts - return success
            return Promise.resolve({
              data: mockCustomerPost,
              error: null,
            });
          }
        }),
        delete: jest.fn().mockReturnThis(),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);
      mockDeleteCustomerPostMedia.mockResolvedValue({ success: true });

      const result = await deletePost(mockPostId);

      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.BUSINESS_POSTS);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.CUSTOMER_POSTS);
      expect(result.success).toBe(true);
      expect(result.message).toBe("Post deleted successfully");
    });

    it("should return error when post not found in either table", async () => {
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: "Not found" },
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Post not found");
      expect(result.error).toBe(
        "The post does not exist or you do not have permission to delete it"
      );
    });

    it("should return error when user is not authenticated", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: "Not authenticated" },
      });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Authentication required");
      expect(result.error).toBe("You must be logged in to delete a post");
    });

    it("should return error when media deletion fails", async () => {
      const mockBusinessPost = {
        id: mockPostId,
        created_at: "2024-01-01T00:00:00Z",
        image_url: "https://example.com/image.jpg",
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockBusinessPost,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);
      mockDeletePostMedia.mockResolvedValue({
        success: false,
        error: "Failed to delete media",
      });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Failed to delete post images");
      expect(result.error).toBe("Cannot delete post: Failed to delete media");
    });

    it("should delete customer post with correct media deletion function", async () => {
      const mockCustomerPost = {
        id: mockPostId,
        created_at: "2024-01-01T00:00:00Z",
        image_url: "test-image.jpg",
      };

      // Mock business post not found, customer post found
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest
          .fn()
          .mockResolvedValueOnce({
            data: null,
            error: { message: "Not found" },
          }) // Business post not found
          .mockResolvedValueOnce({ data: mockCustomerPost, error: null }), // Customer post found
        delete: jest.fn().mockReturnThis(),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);
      mockDeleteCustomerPostMedia.mockResolvedValue({ success: true });

      const result = await deletePost(mockPostId);

      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.BUSINESS_POSTS);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.CUSTOMER_POSTS);
      expect(mockDeleteCustomerPostMedia).toHaveBeenCalledWith(
        mockUser.id,
        mockPostId,
        mockCustomerPost.created_at
      );
      expect(mockDeletePostMedia).not.toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.message).toBe("Post deleted successfully");
    });

    it("should handle customer post media deletion failure", async () => {
      const mockCustomerPost = {
        id: mockPostId,
        created_at: "2024-01-01T00:00:00Z",
        image_url: "test-image.jpg",
      };

      // Mock business post not found, customer post found
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest
          .fn()
          .mockResolvedValueOnce({
            data: null,
            error: { message: "Not found" },
          }) // Business post not found
          .mockResolvedValueOnce({ data: mockCustomerPost, error: null }), // Customer post found
      };

      mockSupabase.from.mockReturnValue(mockFromChain);
      mockDeleteCustomerPostMedia.mockResolvedValue({
        success: false,
        error: "Failed to delete customer media",
      });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Failed to delete post images");
      expect(result.error).toBe(
        "Cannot delete post: Failed to delete customer media"
      );
    });
  });

  describe("updatePostContent", () => {
    const mockUser = { id: "user-123" };
    const mockPostId = "post-456";
    const mockContent = "Updated content";

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should update business post content successfully", async () => {
      const mockUpdatedPost = {
        id: mockPostId,
        content: mockContent,
        updated_at: expect.any(String),
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockPostId },
          error: null,
        }),
        update: jest.fn().mockReturnThis(),
      };

      // Mock the update chain separately
      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === TABLES.BUSINESS_POSTS) {
          return {
            ...mockFromChain,
            update: jest.fn().mockReturnValue(mockUpdateChain),
          };
        }
        return mockFromChain;
      });

      const result = await updatePostContent(mockPostId, mockContent);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post updated successfully");
      expect(result.data).toEqual(mockUpdatedPost);
      expect(mockRevalidatePath).toHaveBeenCalledWith("/dashboard/business");
      expect(mockRevalidatePath).toHaveBeenCalledWith("/dashboard/customer");
    });

    it("should update customer post content when business post not found", async () => {
      const mockUpdatedPost = {
        id: mockPostId,
        content: mockContent,
        updated_at: expect.any(String),
      };

      let selectCallCount = 0;
      const mockFromChain = {
        select: jest.fn().mockImplementation(() => {
          selectCallCount++;
          return {
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockImplementation(() => {
              if (selectCallCount === 1) {
                // First call for business_posts - return error
                return Promise.resolve({
                  data: null,
                  error: { message: "Not found" },
                });
              } else {
                // Second call for customer_posts - return success
                return Promise.resolve({
                  data: { id: mockPostId },
                  error: null,
                });
              }
            }),
          };
        }),
      };

      // Mock the update chain
      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === TABLES.CUSTOMER_POSTS) {
          return {
            ...mockFromChain,
            update: jest.fn().mockReturnValue(mockUpdateChain),
          };
        }
        return mockFromChain;
      });

      const result = await updatePostContent(mockPostId, mockContent);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post updated successfully");
    });

    it("should return error when post not found", async () => {
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: "Not found" },
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      const result = await updatePostContent(mockPostId, mockContent);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Post not found");
      expect(result.error).toBe(
        "The post does not exist or you do not have permission to update it"
      );
    });
  });

  describe("updatePostProducts", () => {
    const mockUser = { id: "user-123" };
    const mockPostId = "post-456";
    const mockProductIds = ["prod-1", "prod-2"];

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should update business post products successfully", async () => {
      const mockUpdatedPost = {
        id: mockPostId,
        product_ids: mockProductIds,
        updated_at: expect.any(String),
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockPostId },
          error: null,
        }),
      };

      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue({
        ...mockFromChain,
        update: jest.fn().mockReturnValue(mockUpdateChain),
      });

      const result = await updatePostProducts(mockPostId, mockProductIds);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post products updated successfully");
      expect(result.data).toEqual(mockUpdatedPost);
    });

    it("should return error when business post not found", async () => {
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: "Not found" },
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      const result = await updatePostProducts(mockPostId, mockProductIds);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Post not found");
      expect(result.error).toBe(
        "The post does not exist or you do not have permission to update it"
      );
    });
  });

  describe("updatePost", () => {
    const mockUser = { id: "user-123" };
    const mockPostId = "post-456";
    const mockFormData = {
      content: "Updated post content",
      image_url: "https://example.com/new-image.jpg",
      product_ids: ["prod-1", "prod-2"],
      mentioned_business_ids: ["biz-1"],
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should update business post with all fields", async () => {
      const mockUpdatedPost = {
        id: mockPostId,
        ...mockFormData,
        updated_at: expect.any(String),
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockPostId },
          error: null,
        }),
      };

      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === TABLES.BUSINESS_POSTS) {
          return {
            ...mockFromChain,
            update: jest.fn().mockReturnValue(mockUpdateChain),
          };
        }
        return mockFromChain;
      });

      const result = await updatePost(mockPostId, mockFormData);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post updated successfully");
      expect(result.data).toEqual(mockUpdatedPost);
    });

    it("should update customer post without business-specific fields", async () => {
      const mockUpdatedPost = {
        id: mockPostId,
        content: mockFormData.content,
        image_url: mockFormData.image_url,
        updated_at: expect.any(String),
      };

      let selectCallCount = 0;
      const mockFromChain = {
        select: jest.fn().mockImplementation(() => {
          selectCallCount++;
          return {
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockImplementation(() => {
              if (selectCallCount === 1) {
                // First call for business_posts - return error
                return Promise.resolve({
                  data: null,
                  error: { message: "Not found" },
                });
              } else {
                // Second call for customer_posts - return success
                return Promise.resolve({
                  data: { id: mockPostId },
                  error: null,
                });
              }
            }),
          };
        }),
      };

      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === TABLES.CUSTOMER_POSTS) {
          return {
            ...mockFromChain,
            update: jest.fn().mockReturnValue(mockUpdateChain),
          };
        }
        return mockFromChain;
      });

      const result = await updatePost(mockPostId, mockFormData);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post updated successfully");
      // Verify that business-specific fields are not included in customer post update
      expect(mockUpdateChain.single).toHaveBeenCalled();
    });
  });

  describe("createPost", () => {
    const mockUser = { id: "user-123" };
    const mockFormData = {
      content: "New business post",
      image_url: "https://example.com/image.jpg",
      product_ids: ["prod-1"],
      mentioned_business_ids: ["biz-1"],
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it("should create business post successfully", async () => {
      const mockBusinessProfile = {
        id: mockUser.id,
        city_slug: "mumbai",
        state_slug: "maharashtra",
        locality_slug: "andheri",
        pincode: "400001",
        logo_url: "https://example.com/logo.jpg",
      };

      const mockCreatedPost = {
        id: "new-post-123",
        business_id: mockUser.id,
        content: mockFormData.content,
        image_url: mockFormData.image_url,
        product_ids: mockFormData.product_ids,
        mentioned_business_ids: mockFormData.mentioned_business_ids,
        city_slug: mockBusinessProfile.city_slug,
        state_slug: mockBusinessProfile.state_slug,
        locality_slug: mockBusinessProfile.locality_slug,
        pincode: mockBusinessProfile.pincode,
        author_avatar: mockBusinessProfile.logo_url,
      };

      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 1 && table === TABLES.BUSINESS_PROFILES) {
          // First call for business profile
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: mockBusinessProfile,
              error: null,
            }),
          };
        } else if (fromCallCount === 2 && table === TABLES.BUSINESS_POSTS) {
          // Second call for post creation
          return {
            insert: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: mockCreatedPost,
              error: null,
            }),
          };
        }
        return {};
      });

      const result = await createPost(mockFormData);

      expect(result.success).toBe(true);
      expect(result.message).toBe("Post created successfully");
      expect(result.data).toEqual(mockCreatedPost);
      expect(mockRevalidatePath).toHaveBeenCalledWith(
        "/dashboard/business/feed"
      );
      expect(mockRevalidatePath).toHaveBeenCalledWith(
        "/dashboard/customer/feed"
      );
    });

    it("should return error when business profile not found", async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: "Profile not found" },
        }),
      });

      const result = await createPost(mockFormData);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Business profile not found");
      expect(result.error).toBe(
        "You must have a business profile to create a post"
      );
    });

    it("should return error when user is not authenticated", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: "Not authenticated" },
      });

      const result = await createPost(mockFormData);

      expect(result.success).toBe(false);
      expect(result.message).toBe("Authentication required");
      expect(result.error).toBe("You must be logged in to create a post");
    });
  });
});
