/**
 * Integration tests for post operations across Next.js and React Native platforms
 * Tests the consistency of post CRUD operations and permission handling
 */

import { createClient } from '@/utils/supabase/server';
import { TABLES, COLUMNS } from '@/lib/supabase/constants';
import {
  createPost,
  deletePost,
  updatePostContent,
  updatePostProducts,
  updatePost,
} from '@/lib/actions/posts/crud';

// Mock dependencies
jest.mock('@/utils/supabase/server');
jest.mock('next/cache');
jest.mock('@/lib/actions/shared/upload-post-media');

const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(),
};

const mockCreateClient = createClient as jest.MockedFunction<typeof createClient>;

describe('Post Operations Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockCreateClient.mockResolvedValue(mockSupabase as any);
  });

  describe('Cross-platform consistency', () => {
    const mockUser = { id: 'user-123' };
    const mockBusinessProfile = {
      id: mockUser.id,
      city_slug: 'mumbai',
      state_slug: 'maharashtra',
      locality_slug: 'andheri',
      pincode: '400001',
      logo_url: 'https://example.com/logo.jpg',
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
    });

    it('should create business posts with consistent data structure', async () => {
      const mockFormData = {
        content: 'Test business post',
        image_url: 'https://example.com/image.jpg',
        product_ids: ['prod-1', 'prod-2'],
        mentioned_business_ids: ['biz-1'],
      };

      const mockCreatedPost = {
        id: 'post-123',
        business_id: mockUser.id,
        content: mockFormData.content,
        image_url: mockFormData.image_url,
        product_ids: mockFormData.product_ids,
        mentioned_business_ids: mockFormData.mentioned_business_ids,
        city_slug: mockBusinessProfile.city_slug,
        state_slug: mockBusinessProfile.state_slug,
        locality_slug: mockBusinessProfile.locality_slug,
        pincode: mockBusinessProfile.pincode,
        author_avatar: mockBusinessProfile.logo_url,
        post_source: 'business',
      };

      let fromCallCount = 0;
      mockSupabase.from.mockImplementation((table) => {
        fromCallCount++;
        if (fromCallCount === 1 && table === TABLES.BUSINESS_PROFILES) {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: mockBusinessProfile,
              error: null,
            }),
          };
        } else if (fromCallCount === 2 && table === TABLES.BUSINESS_POSTS) {
          return {
            insert: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            single: jest.fn().mockResolvedValue({
              data: mockCreatedPost,
              error: null,
            }),
          };
        }
        return {};
      });

      const result = await createPost(mockFormData);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        business_id: mockUser.id,
        content: mockFormData.content,
        product_ids: mockFormData.product_ids,
        mentioned_business_ids: mockFormData.mentioned_business_ids,
      });
    });

    it('should handle post deletion with proper ownership verification', async () => {
      const mockPostId = 'post-456';
      const mockBusinessPost = {
        id: mockPostId,
        created_at: '2024-01-01T00:00:00Z',
        image_url: 'https://example.com/image.jpg',
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockBusinessPost,
          error: null,
        }),
        delete: jest.fn().mockReturnThis(),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      // Mock successful media deletion
      const { deletePostMedia } = require('@/lib/actions/shared/upload-post-media');
      deletePostMedia.mockResolvedValue({ success: true });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(true);
      expect(mockFromChain.eq).toHaveBeenCalledWith(COLUMNS.ID, mockPostId);
      expect(mockFromChain.eq).toHaveBeenCalledWith(COLUMNS.BUSINESS_ID, mockUser.id);
    });

    it('should handle customer post operations correctly', async () => {
      const mockPostId = 'customer-post-789';
      const mockCustomerPost = {
        id: mockPostId,
        created_at: '2024-01-01T00:00:00Z',
        image_url: 'https://example.com/customer-image.jpg',
      };

      let callCount = 0;
      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            // First call for business_posts - return error
            return Promise.resolve({
              data: null,
              error: { message: 'Not found' },
            });
          } else {
            // Second call for customer_posts - return success
            return Promise.resolve({
              data: mockCustomerPost,
              error: null,
            });
          }
        }),
        delete: jest.fn().mockReturnThis(),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      // Mock successful media deletion
      const { deletePostMedia } = require('@/lib/actions/shared/upload-post-media');
      deletePostMedia.mockResolvedValue({ success: true });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.BUSINESS_POSTS);
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.CUSTOMER_POSTS);
    });

    it('should update posts with proper field filtering for customer posts', async () => {
      const mockPostId = 'customer-post-update-123';
      const mockFormData = {
        content: 'Updated customer post content',
        image_url: 'https://example.com/new-image.jpg',
        product_ids: ['prod-1'], // This should be filtered out for customer posts
        mentioned_business_ids: ['biz-1'], // This should be filtered out for customer posts
      };

      const mockUpdatedPost = {
        id: mockPostId,
        content: mockFormData.content,
        image_url: mockFormData.image_url,
        // Note: product_ids and mentioned_business_ids should NOT be included
        updated_at: '2024-01-01T12:00:00Z',
      };

      let selectCallCount = 0;
      const mockFromChain = {
        select: jest.fn().mockImplementation(() => {
          selectCallCount++;
          return {
            eq: jest.fn().mockReturnThis(),
            single: jest.fn().mockImplementation(() => {
              if (selectCallCount === 1) {
                // First call for business_posts - return error
                return Promise.resolve({
                  data: null,
                  error: { message: 'Not found' },
                });
              } else {
                // Second call for customer_posts - return success
                return Promise.resolve({
                  data: { id: mockPostId },
                  error: null,
                });
              }
            }),
          };
        }),
      };

      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === TABLES.CUSTOMER_POSTS) {
          return {
            ...mockFromChain,
            update: jest.fn().mockReturnValue(mockUpdateChain),
          };
        }
        return mockFromChain;
      });

      const result = await updatePost(mockPostId, mockFormData);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        content: mockFormData.content,
        image_url: mockFormData.image_url,
      });
      // Verify that business-specific fields are not included in customer post updates
      expect(result.data).not.toHaveProperty('product_ids');
      expect(result.data).not.toHaveProperty('mentioned_business_ids');
    });

    it('should handle product updates only for business posts', async () => {
      const mockPostId = 'business-post-products-456';
      const mockProductIds = ['prod-1', 'prod-2', 'prod-3'];

      const mockUpdatedPost = {
        id: mockPostId,
        product_ids: mockProductIds,
        updated_at: '2024-01-01T12:00:00Z',
      };

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: { id: mockPostId },
          error: null,
        }),
      };

      const mockUpdateChain = {
        eq: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockUpdatedPost,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue({
        ...mockFromChain,
        update: jest.fn().mockReturnValue(mockUpdateChain),
      });

      const result = await updatePostProducts(mockPostId, mockProductIds);

      expect(result.success).toBe(true);
      expect(result.data).toMatchObject({
        product_ids: mockProductIds,
      });
      expect(mockSupabase.from).toHaveBeenCalledWith(TABLES.BUSINESS_POSTS);
    });
  });

  describe('Permission and ownership validation', () => {
    it('should prevent unauthorized post deletion', async () => {
      const mockUser = { id: 'user-123' };
      const mockPostId = 'unauthorized-post-456';

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Not found' },
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Post not found');
      expect(result.error).toBe(
        'The post does not exist or you do not have permission to delete it'
      );
    });

    it('should prevent unauthenticated operations', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Not authenticated' },
      });

      const result = await deletePost('any-post-id');

      expect(result.success).toBe(false);
      expect(result.message).toBe('Authentication required');
      expect(result.error).toBe('You must be logged in to delete a post');
    });
  });

  describe('Error handling and rollback scenarios', () => {
    it('should handle media deletion failures gracefully', async () => {
      const mockUser = { id: 'user-123' };
      const mockPostId = 'post-with-media-456';
      const mockBusinessPost = {
        id: mockPostId,
        created_at: '2024-01-01T00:00:00Z',
        image_url: 'https://example.com/image.jpg',
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const mockFromChain = {
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockBusinessPost,
          error: null,
        }),
      };

      mockSupabase.from.mockReturnValue(mockFromChain);

      // Mock failed media deletion
      const { deletePostMedia } = require('@/lib/actions/shared/upload-post-media');
      deletePostMedia.mockResolvedValue({
        success: false,
        error: 'Storage service unavailable',
      });

      const result = await deletePost(mockPostId);

      expect(result.success).toBe(false);
      expect(result.message).toBe('Failed to delete post images');
      expect(result.error).toBe('Cannot delete post: Storage service unavailable');
    });
  });
});
