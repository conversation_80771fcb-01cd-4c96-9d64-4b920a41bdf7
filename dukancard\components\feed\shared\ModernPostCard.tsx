"use client";

import Link from "next/link";
import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import { motion } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { PostWithBusinessProfile, ProductData } from "@/lib/types/posts";
import PostActions from "./PostActions";
import ProductListItem from "@/app/components/ProductListItem";
import { fetchProductsByIds } from "@/lib/actions/products/fetchProductsByIds";
import { Loader2 } from "lucide-react";
import {
  MapPin,
  MoreVertical,
  Edit3,
  Trash2,
  Package,
  Share2,
  User,
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import {
  fetchPostAddress,
  formatAddressString,
  formatAddressFromSlugs,
  PostAddress,
} from "@/lib/utils/addressUtils";
import { usePostOwnership } from "@/components/feed/shared/hooks/usePostOwnership";
import InlinePostAndProductEditor from "@/components/feed/shared/editors/InlinePostAndProductEditor";
import InlinePostEditor from "@/components/feed/shared/editors/InlinePostEditor";
import PostDeleteDialog from "@/components/feed/shared/dialogs/PostDeleteDialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { generatePostPath, generatePostUrl } from "@/lib/utils/postUrl";
import { toast } from "sonner";

interface ModernPostCardProps {
  post: PostWithBusinessProfile;
  index?: number;
  onPostUpdate?: (_postId: string, _newContent: string) => void;
  onPostDelete?: (_postId: string) => void;
  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;
  showActualAspectRatio?: boolean; // For single post pages
  disablePostClick?: boolean; // Disable post clicking for single post pages
  enableImageFullscreen?: boolean; // Enable full-screen image viewing
}

export default function ModernPostCard({
  post,
  index = 0,
  onPostUpdate,
  onPostDelete,
  onProductsUpdate,
  showActualAspectRatio = false,
  disablePostClick = false,
  enableImageFullscreen = false,
}: ModernPostCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [address, setAddress] = useState<PostAddress | null>(null);
  const [addressLoading, setAddressLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [currentContent, setCurrentContent] = useState(post.content);
  const [currentProductIds, setCurrentProductIds] = useState<string[]>(
    post.product_ids || []
  );
  const [linkedProducts, setLinkedProducts] = useState<ProductData[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);
  const productsScrollRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [dragDistance, setDragDistance] = useState(0);

  // Check if current user owns this post
  const { isOwner, isLoading: ownershipLoading } = usePostOwnership({
    postBusinessId: post.business_id,
    postCustomerId: post.customer_id,
    postSource: post.post_source,
  });

  // Debug logging
  console.log("ModernPostCard Debug:", {
    postId: post.id,
    postSource: post.post_source,
    postBusinessId: post.business_id,
    postCustomerId: post.customer_id,
    isOwner,
    ownershipLoading,
  });

  // Get business profile data
  const business = post.business_profiles;

  // Handle edit save (both content and products)
  const handleEditSave = (newContent: string, newProductIds: string[]) => {
    setIsEditing(false);
    setCurrentContent(newContent);
    setCurrentProductIds(newProductIds);
    onPostUpdate?.(post.id, newContent);
    onProductsUpdate?.(post.id, newProductIds);
  };

  // Handle edit cancel
  const handleEditCancel = () => {
    setIsEditing(false);
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    setShowDeleteDialog(false);
    onPostDelete?.(post.id);
  };

  const handleShare = async () => {
    try {
      const postUrl = generatePostUrl(post.id);

      // Try to use native Web Share API if available
      if (navigator.share) {
        await navigator.share({
          title: "Check out this post on Dukancard",
          url: postUrl,
        });
        return;
      }

      // Fallback to clipboard API
      await navigator.clipboard.writeText(postUrl);
      toast.success("Post link copied to clipboard!");
    } catch (error) {
      console.error("Error sharing post:", error);
      toast.error("Failed to share post");
    }
  };

  // Fetch linked products
  useEffect(() => {
    if (currentProductIds.length === 0) {
      setLinkedProducts([]);
      setIsLoadingProducts(false);
      return;
    }

    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const result = await fetchProductsByIds(currentProductIds);

        if (!result.success) {
          console.error("Error fetching products:", result.error);
          setLinkedProducts([]);
          return;
        }

        // Maintain the order of products as specified in currentProductIds array
        const orderedProducts = currentProductIds
          .map((id) => result.data?.find((product) => product.id === id))
          .filter(Boolean) as ProductData[];

        setLinkedProducts(orderedProducts);
      } catch (err) {
        console.error("Error fetching products:", err);
        setLinkedProducts([]);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [currentProductIds]);

  // Add drag-to-scroll functionality for products container
  useEffect(() => {
    const container = productsScrollRef.current;
    if (!container) return;

    const handleMouseDown = (e: MouseEvent) => {
      if (container.scrollWidth <= container.clientWidth) return; // No overflow, no need to scroll

      setIsDragging(true);
      setStartX(e.pageX - container.offsetLeft);
      setScrollLeft(container.scrollLeft);
      setDragDistance(0);
      container.style.cursor = "grabbing";
      e.preventDefault();
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || container.scrollWidth <= container.clientWidth) return;

      e.preventDefault();
      const x = e.pageX - container.offsetLeft;
      const walk = (x - startX) * 2; // Scroll speed multiplier
      const newDragDistance = Math.abs(x - startX);
      setDragDistance(newDragDistance);
      container.scrollLeft = scrollLeft - walk;
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      container.style.cursor =
        container.scrollWidth > container.clientWidth ? "grab" : "default";
    };

    const handleMouseLeave = () => {
      setIsDragging(false);
      container.style.cursor =
        container.scrollWidth > container.clientWidth ? "grab" : "default";
    };

    // Set initial cursor
    container.style.cursor =
      container.scrollWidth > container.clientWidth ? "grab" : "default";

    container.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    container.addEventListener("mouseleave", handleMouseLeave);

    return () => {
      container.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
      container.removeEventListener("mouseleave", handleMouseLeave);
    };
  }, [linkedProducts, isDragging, startX, scrollLeft, dragDistance]);

  // Handle product click - only navigate if it wasn't a drag
  const handleProductClick = (e: React.MouseEvent, productUrl: string) => {
    if (dragDistance > 5) {
      // If dragged more than 5px, don't navigate
      e.preventDefault();
      return;
    }
    // Allow normal navigation for clicks
    window.open(productUrl, "_blank", "noopener,noreferrer");
  };

  // Fetch real address data - moved before conditional return
  useEffect(() => {
    if (!business) return; // Early return if no business data

    const loadAddress = async () => {
      setAddressLoading(true);
      try {
        const addressData = await fetchPostAddress(
          post.locality_slug,
          post.city_slug,
          post.state_slug,
          post.pincode
        );
        setAddress(addressData);
      } catch (error) {
        console.error("Error fetching address:", error);
        setAddress(null);
      } finally {
        setAddressLoading(false);
      }
    };

    loadAddress();
  }, [
    business,
    post.locality_slug,
    post.city_slug,
    post.state_slug,
    post.pincode,
  ]);

  if (!business) {
    return null; // Skip rendering if business profile is missing
  }

  // Format the post date
  const formattedDate = formatDistanceToNow(new Date(post.created_at), {
    addSuffix: true,
  });

  // Avatar fallback now uses User icon instead of initials

  // Format address display
  const getDisplayAddress = () => {
    if (addressLoading) {
      return "Loading address...";
    }

    if (address) {
      return formatAddressString(address);
    }

    // Fallback to slug-based formatting
    return formatAddressFromSlugs(
      post.locality_slug,
      post.city_slug,
      post.state_slug,
      post.pincode
    );
  };

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        delay: index * 0.1,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "bg-white dark:bg-black",
        "overflow-hidden mb-4 md:mb-6",
        // Only show card styling on desktop
        "md:rounded-xl md:border md:border-neutral-200 md:dark:border-neutral-800 md:shadow-sm md:hover:shadow-md md:transition-all md:duration-300"
      )}
    >
      {/* Post Header */}
      <div className="p-4 pb-2">
        {/* Business Info and Time */}
        <div className="flex items-center justify-between mb-3">
          {business.business_slug ? (
            <Link
              href={`/${business.business_slug}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-3 flex-1 min-w-0 group"
            >
              <Avatar className="h-12 w-12 border-2 border-[var(--brand-gold)]/30 transition-transform group-hover:scale-105">
                <AvatarImage
                  src={business.logo_url || ""}
                  alt={business.business_name || "Business"}
                  className="object-cover"
                />
                <AvatarFallback className="bg-muted text-foreground border border-[var(--brand-gold)]/30">
                  <User className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors">
                  {business.business_name}
                </h3>
                {business.business_slug && (
                  <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-0.5">
                    @{business.business_slug}
                  </div>
                )}
                <div className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                  {formattedDate}
                </div>
              </div>
            </Link>
          ) : (
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-12 w-12 border-2 border-[var(--brand-gold)]/30">
                <AvatarImage
                  src={business.logo_url || ""}
                  alt={business.business_name || "Customer"}
                  className="object-cover"
                />
                <AvatarFallback className="bg-muted text-foreground border border-[var(--brand-gold)]/30">
                  <User className="h-6 w-6" />
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate">
                  {business.business_name}
                </h3>
                <div className="text-xs text-neutral-400 dark:text-neutral-500 mt-1">
                  {formattedDate}
                </div>
              </div>
            </div>
          )}
          {/* Three-dot menu - show for all posts */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full"
              >
                <MoreVertical className="h-5 w-5 text-neutral-500 dark:text-neutral-400" />
                <span className="sr-only">Open post menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {/* Share option - available for all posts */}
              <DropdownMenuItem
                onClick={handleShare}
                className="cursor-pointer"
              >
                <Share2 className="h-4 w-4 mr-2" />
                Share post
              </DropdownMenuItem>

              {/* Owner-only options */}
              {isOwner && !ownershipLoading && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setIsEditing(true)}
                    className="cursor-pointer"
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit post
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setShowDeleteDialog(true)}
                    className="text-destructive focus:text-destructive cursor-pointer"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete post
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Address */}
        <div className="flex items-center text-sm text-neutral-500 dark:text-neutral-400">
          <MapPin className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
          <span>{getDisplayAddress()}</span>
        </div>
      </div>

      {/* Post Content */}
      <div className="px-4 pb-3">
        {isEditing ? (
          post.post_source === "customer" ? (
            <InlinePostEditor
              postId={post.id}
              initialContent={currentContent}
              onSave={(newContent) => handleEditSave(newContent, [])}
              onCancel={handleEditCancel}
            />
          ) : (
            <InlinePostAndProductEditor
              postId={post.id}
              initialContent={currentContent}
              initialProductIds={currentProductIds}
              initialImageUrl={post.image_url}
              onSave={handleEditSave}
              onCancel={handleEditCancel}
            />
          )
        ) : disablePostClick ? (
          <p className="text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line">
            {currentContent}
          </p>
        ) : (
          <Link
            href={generatePostPath(post.id)}
            className="block cursor-pointer"
          >
            <p className="text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line">
              {currentContent}
            </p>
          </Link>
        )}
      </div>

      {/* Post Image */}
      {post.image_url &&
        (disablePostClick ? (
          <div
            className={cn(
              "relative w-full transition-opacity duration-200",
              enableImageFullscreen ? "cursor-pointer hover:opacity-95" : ""
            )}
            onClick={
              enableImageFullscreen
                ? () => setShowFullscreenImage(true)
                : undefined
            }
          >
            {showActualAspectRatio ? (
              <div className="relative w-full bg-neutral-100 dark:bg-neutral-800">
                <Image
                  src={post.image_url}
                  alt="Post image"
                  width={800}
                  height={600}
                  className={cn(
                    "w-full h-auto object-contain transition-all duration-300",
                    imageLoading && "blur-sm scale-105",
                    imageError && "hidden"
                  )}
                  onLoad={() => setImageLoading(false)}
                  onError={() => {
                    setImageError(true);
                    setImageLoading(false);
                  }}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority={index < 3}
                />
                {imageLoading && (
                  <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
                )}
              </div>
            ) : (
              <div className="relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800">
                <Image
                  src={post.image_url}
                  alt="Post image"
                  fill
                  className={cn(
                    "object-cover transition-all duration-300",
                    imageLoading && "blur-sm scale-105",
                    imageError && "hidden"
                  )}
                  onLoad={() => setImageLoading(false)}
                  onError={() => {
                    setImageError(true);
                    setImageLoading(false);
                  }}
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority={index < 3}
                />
                {imageLoading && (
                  <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
                )}
              </div>
            )}
          </div>
        ) : (
          <Link href={generatePostPath(post.id)} className="block">
            <div className="relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200">
              {showActualAspectRatio ? (
                <div className="relative w-full bg-neutral-100 dark:bg-neutral-800">
                  <Image
                    src={post.image_url}
                    alt="Post image"
                    width={800}
                    height={600}
                    className={cn(
                      "w-full h-auto object-contain transition-all duration-300",
                      imageLoading && "blur-sm scale-105",
                      imageError && "hidden"
                    )}
                    onLoad={() => setImageLoading(false)}
                    onError={() => {
                      setImageError(true);
                      setImageLoading(false);
                    }}
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority={index < 3}
                  />
                  {imageLoading && (
                    <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
                  )}
                </div>
              ) : (
                <div className="relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800">
                  <Image
                    src={post.image_url}
                    alt="Post image"
                    fill
                    className={cn(
                      "object-cover transition-all duration-300",
                      imageLoading && "blur-sm scale-105",
                      imageError && "hidden"
                    )}
                    onLoad={() => setImageLoading(false)}
                    onError={() => {
                      setImageError(true);
                      setImageLoading(false);
                    }}
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    priority={index < 3}
                  />
                  {imageLoading && (
                    <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse" />
                  )}
                </div>
              )}
            </div>
          </Link>
        ))}

      {/* Linked Products */}
      {currentProductIds.length > 0 && !isEditing && (
        <div className="px-4 pt-4">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg">
                <Package className="h-4 w-4 text-[var(--brand-gold)]" />
              </div>
              <h4 className="text-sm font-semibold text-neutral-900 dark:text-neutral-100">
                Featured Products ({linkedProducts.length})
              </h4>
            </div>

            {isLoadingProducts ? (
              <div className="flex justify-center py-8">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin text-[var(--brand-gold)]" />
                  <span className="text-xs text-muted-foreground">
                    Loading products...
                  </span>
                </div>
              </div>
            ) : (
              <motion.div
                ref={productsScrollRef}
                className="flex gap-3 overflow-x-auto scrollbar-hide pb-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
              >
                {linkedProducts.map((product) => (
                  <motion.div
                    key={product.id}
                    className="flex-shrink-0 w-40"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div
                      className="block h-full cursor-pointer"
                      onClick={(e) => {
                        if (business.business_slug) {
                          handleProductClick(
                            e,
                            `/${business.business_slug}/product/${
                              product.slug || product.id
                            }`
                          );
                        }
                      }}
                    >
                      <ProductListItem
                        product={{
                          ...product,
                          // Convert to ProductServiceData format
                          description: undefined,
                          product_type: "physical" as const,
                          base_price: product.base_price || 0,
                          slug: product.slug || undefined,
                          is_available: true,
                          images: product.image_url
                            ? [product.image_url]
                            : undefined,
                          featured_image_index: 0,
                          created_at: new Date().toISOString(),
                          updated_at: new Date().toISOString(),
                        }}
                        isLink={false}
                      />
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </div>
        </div>
      )}

      {/* Post Actions - Full Width */}
      <div className="p-4 pt-3">
        <PostActions
          business={business}
          hasWhatsApp={
            !!(
              business.whatsapp_number && business.whatsapp_number.trim() !== ""
            )
          }
          hasPhone={!!(business.phone && business.phone.trim() !== "")}
          _postId={post.id}
          onShare={handleShare}
        />
      </div>

      {/* Delete Dialog */}
      <PostDeleteDialog
        isOpen={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        postId={post.id}
        postContent={currentContent}
        onDeleteSuccess={handleDeleteSuccess}
      />

      {/* Full-screen Image Modal */}
      {enableImageFullscreen && showFullscreenImage && post.image_url && (
        <div
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
          onClick={() => setShowFullscreenImage(false)}
        >
          <div className="relative max-w-full max-h-full">
            <button
              onClick={() => setShowFullscreenImage(false)}
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
            <Image
              src={post.image_url}
              alt="Post image fullscreen"
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}
    </motion.div>
  );
}
