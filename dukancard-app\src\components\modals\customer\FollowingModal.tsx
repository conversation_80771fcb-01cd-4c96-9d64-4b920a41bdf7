import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createFollowingModalStyles } from "@/styles/modals/customer/following-modal";
import { FollowingList } from "./components/FollowingList";
import { useNavigation } from "@react-navigation/native";
import { RootStackParamList } from "@/src/types/navigation";
import { StackNavigationProp } from "@react-navigation/stack";

type FollowingModalNavigationProp = StackNavigationProp<
  RootStackParamList,
  "Profile"
>;

interface FollowingModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function FollowingModal({
  visible,
  onClose,
}: FollowingModalProps) {
  const theme = useTheme();
  const styles = createFollowingModalStyles(theme);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");
  const navigation = useNavigation<FollowingModalNavigationProp>();

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  const handleDiscoverPress = () => {
    onClose();
    navigation.navigate("Discover");
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            <View style={styles.header}>
              <View style={{ width: 40 }} />
              <Text style={styles.headerTitle} testID="header-title">
                Following
              </Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={onClose}
                testID="close-button"
                accessibilityRole="button"
                accessibilityLabel="Close"
              >
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>
            <View style={styles.contentContainer}>
              <View style={styles.searchContainer}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    backgroundColor: theme.isDark
                      ? theme.colors.input
                      : theme.colors.card,
                    borderRadius: theme.borderRadius.md,
                    paddingHorizontal: theme.spacing.md,
                    borderWidth: 1,
                    borderColor: theme.colors.border,
                  }}
                >
                  <Search
                    size={20}
                    color={theme.colors.textSecondary}
                    style={{ marginRight: theme.spacing.sm }}
                  />
                  <TextInput
                    style={{
                      flex: 1,
                      height: 48,
                      color: theme.colors.foreground,
                      fontSize: theme.typography.fontSize.base,
                    }}
                    placeholder="Search by business name..."
                    placeholderTextColor={theme.colors.textSecondary}
                    value={searchTerm}
                    onChangeText={setSearchTerm}
                    onSubmitEditing={handleSearchSubmit}
                    returnKeyType="search"
                    testID="search-input"
                  />
                  {searchTerm.length > 0 && (
                    <TouchableOpacity
                      onPress={handleSearch}
                      style={{
                        padding: theme.spacing.xs,
                        marginLeft: theme.spacing.xs,
                      }}
                    >
                      <Search size={20} color={theme.colors.primary} />
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <FollowingList
                searchTerm={activeSearchTerm}
                onDiscoverPress={handleDiscoverPress}
              />
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </View>
    </Modal>
  );
}
