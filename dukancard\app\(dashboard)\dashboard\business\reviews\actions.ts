"use server";

import { reviewsService } from "@/lib/services/socialService";

export async function fetchBusinessReviewsReceived(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest"
) {
  try {
    const result = await reviewsService.fetchBusinessReviewsReceived(
      businessId,
      page,
      limit,
      sortBy
    );
    return { success: true, data: result };
  } catch (error) {
    console.error(
      "Error in fetchBusinessReviewsReceived server action:",
      error
    );
    return { success: false, error: (error as Error).message };
  }
}

export async function fetchMyReviews(
  userId: string,
  page: number = 1,
  limit: number = 10,
  sortBy: "newest" | "oldest" | "rating_high" | "rating_low" = "newest"
) {
  try {
    const result = await reviewsService.fetchReviews(
      userId,
      page,
      limit,
      sortBy
    );
    return { success: true, data: result };
  } catch (error) {
    console.error("Error in fetchMyReviews server action:", error);
    return { success: false, error: (error as Error).message };
  }
}
