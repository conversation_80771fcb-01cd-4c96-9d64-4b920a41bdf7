{"name": "dukancard-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "NODE_ENV=development expo run:android", "android:release": "NODE_ENV=production expo run:android --variant release", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "test": "set NODE_OPTIONS=--experimental-vm-modules && jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "prebuild:android": "node scripts/preserve-keystore.js backup && npx expo prebuild --platform android --clean && node scripts/preserve-keystore.js restore", "encrypt-keys": "node scripts/encrypt-keys.js", "build:secure-native": "npm run encrypt-keys && npm run android:release", "test:performance": "reassure", "dep-graph": "depcruise --include-only \"^app|^src|^lib\" --output-type json --output-to dependency-graph.json ."}, "dependencies": {"@bottom-tabs/react-navigation": "^0.9.2", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^4.6.4", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "11.4.1", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.4", "@supabase/supabase-js": "^2.49.8", "ajv": "^8.17.1", "base64-arraybuffer": "^1.0.2", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-camera": "~16.1.10", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-media-library": "~17.1.7", "expo-router": "~5.1.3", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.514.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.59.0", "react-native": "0.79.5", "react-native-bottom-tabs": "^0.9.2", "react-native-config": "^1.5.5", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-reanimated": "~3.17.4", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-qr-code": "^2.0.16", "ts-interface-checker": "^1.0.2", "yup": "^1.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-hooks": "^8.0.1", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.0", "@types/react-test-renderer": "^19.1.0", "@types/uuid": "^10.0.0", "dependency-cruiser": "^16.10.4", "detox": "^20.40.0", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "~29.7.0", "jest-environment-node": "^30.0.0", "msw": "^2.10.3", "react-test-renderer": "19.0.0", "reassure": "^1.4.0", "typescript": "~5.8.3"}, "private": true}