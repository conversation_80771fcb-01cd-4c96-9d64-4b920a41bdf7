import React, { useState, useRef, useEffect, useCallback } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, ChevronDown, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createReviewsModalStyles } from "@/styles/modals/customer/reviews-modal";
import ReviewsList from "./components/ReviewsList";
import ReviewsSortBottomSheet, {
  ReviewsSortBottomSheetRef,
  ReviewSortOption,
} from "./components/ReviewsSortBottomSheet";
import { useAuth } from "@/src/contexts/AuthContext";
import { reviewsService } from "@/backend/supabase/services/posts/socialService";
import { formatIndianNumberShort } from "@/lib/utils";
import { useNavigation } from "@react-navigation/native";
import { RootStackParamList } from "@/src/types/navigation";
import { StackNavigationProp } from "@react-navigation/stack";

type ReviewsModalNavigationProp = StackNavigationProp<
  RootStackParamList,
  "Profile"
>;

interface ReviewsModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function ReviewsModal({ visible, onClose }: ReviewsModalProps) {
  const theme = useTheme();
  const styles = createReviewsModalStyles(theme);
  const { user } = useAuth();
  const sortBottomSheetRef = useRef<ReviewsSortBottomSheetRef>(null);
  const navigation = useNavigation<ReviewsModalNavigationProp>();

  const [sortBy, setSortBy] = useState<ReviewSortOption>("newest");
  const [reviewCount, setReviewCount] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  const handleDiscoverPress = () => {
    onClose();
    navigation.navigate("Discover");
  };

  // Fetch review count
  const fetchReviewCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      const result = await reviewsService.fetchReviews(user.id, 1, 1, sortBy);
      setReviewCount(result.totalCount);
    } catch (error) {
      console.error("Failed to fetch review count:", error);
    }
  }, [user?.id, sortBy]);

  useEffect(() => {
    if (visible) {
      fetchReviewCount();
    }
  }, [visible, fetchReviewCount]);

  const handleSortPress = () => {
    sortBottomSheetRef.current?.present();
  };

  const handleSortSelect = (newSortBy: ReviewSortOption) => {
    setSortBy(newSortBy);
  };

  const getSortDisplayName = (sort: ReviewSortOption): string => {
    switch (sort) {
      case "newest":
        return "Newest First";
      case "oldest":
        return "Oldest First";
      case "rating_high":
        return "Highest Rating";
      case "rating_low":
        return "Lowest Rating";
      default:
        return "Newest First";
    }
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.header}>
            <View style={{ width: 40 }} />
            <Text style={styles.headerTitle}>My Reviews</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Review Count and Sort Section */}
          <View style={styles.reviewsHeaderSection}>
            <Text style={styles.reviewCountText}>
              {formatIndianNumberShort(reviewCount)}{" "}
              {reviewCount === 1 ? "review" : "reviews"}
            </Text>
            <TouchableOpacity
              style={styles.sortButton}
              onPress={handleSortPress}
            >
              <Text style={styles.sortButtonText}>
                {getSortDisplayName(sortBy)}
              </Text>
              <ChevronDown size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Search Section */}
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <Search
                size={20}
                color={theme.colors.textSecondary}
                style={{ marginRight: theme.spacing.sm }}
              />
              <TextInput
                style={styles.searchInput}
                placeholder="Search by business name..."
                placeholderTextColor={theme.colors.textSecondary}
                value={searchTerm}
                onChangeText={setSearchTerm}
                onSubmitEditing={handleSearchSubmit}
                returnKeyType="search"
              />
              {searchTerm.length > 0 && (
                <TouchableOpacity
                  onPress={handleSearch}
                  style={{
                    padding: theme.spacing.xs,
                    marginLeft: theme.spacing.xs,
                  }}
                >
                  <Search size={20} color={theme.colors.primary} />
                </TouchableOpacity>
              )}
            </View>
          </View>

          <View style={styles.contentContainer}>
            <ReviewsList
              sortBy={sortBy}
              searchTerm={activeSearchTerm}
              onReviewCountChange={setReviewCount}
              onDiscoverPress={handleDiscoverPress}
            />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      {/* Sort Bottom Sheet */}
      <ReviewsSortBottomSheet
        ref={sortBottomSheetRef}
        sortBy={sortBy}
        onSortSelect={handleSortSelect}
      />
    </Modal>
  );
}
